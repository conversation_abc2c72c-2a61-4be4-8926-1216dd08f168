import { ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { TabViewModule } from 'cax-design-system/tabview';
import { ButtonModule } from 'cax-design-system/button';
import { TableModule } from 'cax-design-system/table';
import { SplitButtonModule } from 'cax-design-system/splitbutton';
import { PaginatorModule } from 'cax-design-system/paginator';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BatchRowsComponent } from './batch-rows/batch-rows.component';
import { SidebarModule } from 'cax-design-system/sidebar';
import { OverlayPanelModule } from 'cax-design-system/overlaypanel';
import { ChipModule } from 'cax-design-system/chip';
import { AutoCompleteModule } from 'cax-design-system/autocomplete';
import { ConfirmationService } from 'cax-design-system/api';
import { ConfirmDialogModule } from 'cax-design-system/confirmdialog';
import { BadgeModule } from 'cax-design-system/badge';
import { TableconfigurationModule } from 'cax-design-system/tableconfiguration';
import { ColumnListSidebarComponent } from './column-list-sidebar/column-list-sidebar.component';
import { UploadBatchSidebarComponent } from './upload-batch-sidebar/upload-batch-sidebar.component';
import { FiltersListPanelComponent } from './filters-list-panel/filters-list-panel.component';
import { TimelineModule } from 'cax-design-system/timeline';
import { DialogModule } from 'cax-design-system/dialog';
import { CommentboxModule } from 'cax-design-system/commentbox';
import { AvatarModule } from 'cax-design-system/avatar';
import { ReworkDialogComponent } from './rework-dialog/rework-dialog.component';
import { InputTextModule } from 'cax-design-system/inputtext';
import { DropdownModule } from 'cax-design-system/dropdown';
import { Calendar } from 'cax-design-system/calendar';
import { ActivatedRoute, Router } from '@angular/router';
import { CreateNewTagSidebarComponent } from '../tags/create-new-tag-sidebar/create-new-tag-sidebar.component';
import { DividerModule } from 'cax-design-system/divider';
import { MultiSelectModule } from 'cax-design-system/multiselect';
import { ToastModule } from 'cax-design-system/toast';
import { ProgressBarModule } from 'cax-design-system/progressbar';
import { MessageService } from 'cax-design-system/api';

// Service imports
import { HomeService } from '../../../services/home.service';
import { CommentsService } from '../../../services/comments.service';
import { UserService } from '../../../services/user.service';
import { ProductsService } from '../../../services/products.service';
import { ProductDetailsService } from '../../../services/product-details.service';
import { ReviewService } from '../../../services/review.service';

// RxJS imports
import { Subject, timer } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';

interface OverlayTag {
  id: number;
  name: string;
  slug: string;
  bg_colour_code: string;
  text_colour_code: string;
  severity: ChipSeverity;
  customStyle?: {
    backgroundColor: string;
    color: string;
  };
}
type ChipSeverity = 'primary' | 'secondary' | 'success'  | 'warning' | 'error';
@Component({
    selector: 'app-batches',
    standalone: true,
    imports: [
        TabViewModule,
        ButtonModule,
        TableModule,
        CommonModule,
        FormsModule,
        SplitButtonModule,
        PaginatorModule,
        SidebarModule,
        OverlayPanelModule,
        ChipModule,
        AutoCompleteModule,
        ConfirmDialogModule,
        BadgeModule,
        TableconfigurationModule,
        ColumnListSidebarComponent,
        UploadBatchSidebarComponent,
        FiltersListPanelComponent,
        TimelineModule,
        DialogModule,
        CommentboxModule,
        AvatarModule,
        ReworkDialogComponent,
        InputTextModule,
        DropdownModule,
        SplitButtonModule,
        Calendar,
        FormsModule,
        CreateNewTagSidebarComponent,
         DividerModule,
         MultiSelectModule,
         ToastModule,
         ProgressBarModule,
    ],
    templateUrl: './batches.component.html',
    styleUrl: './batches.component.scss',
    providers: [ConfirmationService, MessageService],
})

export class BatchesComponent implements OnInit, OnDestroy {

    // Tag filter options for cax-multiSelect
    tagFilterOptions: { id: number, name: string, slug: string, bg_colour_code: string, text_colour_code: string }[] = [];
    selectedTags: string[] = []; // Array to store selected tag IDs
    public object = Object;
    tagsName: string = '';
    filteredTags: OverlayTag[] = [];
    pinnedTabs: number[] = [0];
    activeTabIndex: number = 0;
    batchFontSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    batchRowSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    avatarSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    inputSize:'sm'| 'md'| 'lg'= 'md';
    size: 'sm' | 'md' | 'lg' = 'md';
    buttonSize : 'small' | 'large' | 'medium' = 'medium';
    batchList: any[] = [];
    batchTableColumns: any[] = [];
    dynamicTabs: any[] = [];
    columnSidebarVisible: boolean = false;
    uploadSidebarVisible: boolean = false;
    selectionSidebarVisible: boolean = false;
    reworkDialogVisible: boolean = false;
    // API-driven data
    batchStatusList: any[] = [];
    batchStatusPanelList: any[] = [];
    batchStatusRowIndex: number = 0;
    batchLogList: any[] = [];
    commentsSidebarVisible: boolean = false;
    currentBatchComments: any[] = [];
    currentBatchId: string = '';
    currentBatchName: string = '';
    currentBatchHeader: string = '';
    selectedBatches: any[] = [];
    selectedBatchesComment: string = '';
    selectedBatchStatus: string = '';
    statusOptions: any[] = [];

    // API-driven suggestions
    mentionSuggestions: any[] = [];
    hashtagSuggestions: any[] = [];
    assigneeOptions: any[] = [];

    // Subscription management
    subscriptionId: string = '';

    // Loading states
    isLoading: boolean = false;
    isLoadingBatches: boolean = false;
    selectedBatchETA: string = '';
    selectedBatchAssignee: string = '';
    batchTableFilters = {};
    selectedItem: [] | undefined;
    selectedRowForTags: any;
    selectedDate: Date | null = null;
    previousDate: Date | null = null;
    currentRowData: any = null;
    isCreateTagSidebarOpen: boolean = false;
    openComment: boolean = false;

    // API-driven search items
    allItems: any[] = [];
    filteredItems: any[] = [];

    // API-driven tags
    overlayTags: OverlayTag[] = [];

    // Tab management properties
    statusTabs: any[] = [];
    currentStatus: string = '';
    activeStatusTabIndex: number = 0;

    // Pagination properties
    currentPage: number = 1;
    pageSize: number = 50;
    totalPages: number = 1;
    totalRecords: number = 0;

    // Filter properties
    searchText: string = '';
    startDate: string = '';
    endDate: string = '';
    assignedTo: string = '';
    priority: string[] = [];
    dateRange: Date[] = [];

    // Manual refresh properties

    // File operations
    fileDownloadProgress: { [key: string]: number } = {};
    fileGenerationStatus: { [key: string]: string } = {};
    toolTipMessage: string = '';

    // Export functionality
    exportDownloadUrl: string = '';
    private stopPolling$ = new Subject<boolean>();

    // Bulk operations
    bulkActionInProgress: boolean = false;
    selectedItemsForBulkAction: any[] = [];

    // User permissions
    userPermissions: any = null;
    canApprove: boolean = false;
    canDelete: boolean = false;
    canEdit: boolean = false;
    canDownload: boolean = false;

    // Templates
    availableTemplates: any[] = [];

    // Error handling
    errorMessage: string = '';
    showError: boolean = false;



    @ViewChild('batchStatusPanel') statusPanel: any;
    @ViewChild('batchLogPanel') batchLogPanel: any;
    @ViewChild('calendalPanel') calendalPanel: any;
    @ViewChild('tagsPanel') tagsPanel: any;



    constructor(
        private homeService: HomeService,
        private commentsService: CommentsService,
        private userService: UserService,
        private productsService: ProductsService,
        private productDetailsService: ProductDetailsService,
        private reviewService: ReviewService,
        private cdr: ChangeDetectorRef,
        private confirmationService: ConfirmationService,
        private router: Router,
        private route: ActivatedRoute,
        private messageService: MessageService
    ) {
        // Initialize with empty columns - will be loaded from API
        this.batchTableColumns = [];
    }

    ngOnInit() {
        this.initializeStatusTabs();
        this.initializeSubscriptionId(); // This will call loadInitialData when subscription ID is ready
        this.loadUserPermissions();
        this.loadTemplates();

        // Ensure tabs are always visible even if API calls fail
        setTimeout(() => {
            if (!this.statusTabs || this.statusTabs.length === 0) {
                this.statusTabs = this.getDefaultTabs();
            }
        }, 2000);
    }

    ngOnDestroy() {
        // Stop export polling if active
        this.stopPolling$.next(true);
        this.stopPolling$.complete();
    }

    private initializeSubscriptionId() {
        // Get subscription ID from query params or localStorage
        this.route.queryParams.subscribe(params => {
            if (params['sub']) {
                this.subscriptionId = params['sub'];
                localStorage.setItem('SubscriptionID', this.subscriptionId);
            } else {
                this.subscriptionId = localStorage.getItem('SubscriptionID') || '';
            }

            // Load initial data after subscription ID is set
            if (this.subscriptionId) {
                this.loadInitialData();
            }
        });
    }

    private initializeStatusTabs() {
        // Initialize status tabs with default values - similar to module-r2e pattern
        this.statusTabs = this.getDefaultTabs();
        this.activeStatusTabIndex = 0;
        this.currentStatus = this.statusTabs[0]?.status || '';
    }

    private loadInitialData() {
        if (!this.subscriptionId) {
            console.error('No subscription ID available');
            return;
        }

        this.isLoading = true;

        // Load stats first to get tab counts, then load other data
        this.loadStats();
        this.loadBatchList();
        this.loadLabelList();
        this.loadUserNamesToTag();
        this.loadUserDetails();
        this.loadReviewFilters();

        // Set loading to false after a short delay to allow all subscriptions to complete
        setTimeout(() => {
            this.isLoading = false;
        }, 1000);
    }

    private loadBatchList() {
        this.isLoadingBatches = true;

        console.log(`Loading batch list for status: ${this.currentStatus}, page: ${this.currentPage}`);

        // Load the batches for current status
        return this.homeService.getBatchList(
            this.currentPage.toString(),
            this.pageSize.toString(),
            this.currentStatus,
            this.searchText,
            this.startDate,
            this.endDate,
            this.subscriptionId,
            this.selectedTags
        ).subscribe({
            next: (response) => {
                console.log(`Batch list loaded successfully for status: ${this.currentStatus}`, response);

                // Transform the API data to match expected format
                this.batchList = this.transformBatchData(response.result || []);

                // Update pagination data from API response
                this.currentPage = response.page || 1;
                this.totalPages = response.total_pages || 1;
                this.totalRecords = response.total_items || 0;
                this.pageSize = response.page_size || 50;

                // Comments will be loaded only when sidebar is opened

                // Initialize table columns if not already set
                if (this.batchTableColumns.length === 0) {
                    this.initializeTableColumns();
                }

                this.isLoadingBatches = false;

                // Force change detection after loading
                this.cdr.detectChanges();
            },
            error: (error) => {
                console.error(`Error loading batch list for status: ${this.currentStatus}`, error);
                this.batchList = [];
                this.isLoadingBatches = false;
                this.showErrorToast('Error Loading Batches', 'Failed to load batch data. Please try again.');
            }
        });
    }

    private initializeTableColumns(): void {
        // Get columns specific to the current tab status
        this.batchTableColumns = this.getColumnsForCurrentTab();
    }

    private getColumnsForCurrentTab(): any[] {
        const currentStatus = this.getCurrentTabStatus();

        // Define common columns that appear in multiple tabs
        const commonColumns = {
            batch_id: {
                field: 'batch_id',
                header: 'Batch ID',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px',
                fixed: true
            },
            name: {
                field: 'name',
                header: 'Name',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '200px',
                maxWidth: '300px',

            },
            description: {
                field: 'description',
                header: 'Description',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '200px',
                maxWidth: '300px'
            },
            tags: {
                field: 'tags',
                header: 'Tags',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            created_at: {
                field: 'created_at',
                header: 'Created On',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            eta: {
                field: 'eta',
                header: 'ETA',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            references: {
                field: 'references',
                header: 'References',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '150px',
                maxWidth: '200px'
            },
            supplier_connector_batch_id: {
                field: 'supplier_connector_batch_id',
                header: 'SC Batch ID',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            accepted_rows: {
                field: 'accepted_rows',
                header: 'Accepted Rows',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            progress_percent: {
                field: 'progress_percent',
                header: 'Progress',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '100px',
                maxWidth: '120px'
            },
            actions: {
                field: 'actions',
                header: 'Actions',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '150px',
                maxWidth: '200px',
                frozen: true,

            },
            comments: {
                field: 'comments',
                header: 'Comments',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '150px',
                maxWidth: '200px'
            },
            download: {
                field: 'download',
                header: 'Download',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '150px',
                maxWidth: '200px',
                frozen: true,

            },
            status: {
                field: 'status',
                header: 'Status',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            total_rows: {
                field: 'total_rows',
                header: 'Total Rows',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '100px',
                maxWidth: '120px'
            },
            created_by: {
                field: 'created_by',
                header: 'Created By',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            },
            rejected_rows: {
                field: 'rejected_rows',
                header: 'Rejected Rows',
                sortable: false,
                filter: false,
                selected: true,
                minWidth: '120px',
                maxWidth: '150px'
            }
        };

        // Define column configurations for each tab
        let columns: any[] = [];
        let order = 1;

        switch (currentStatus) {
            case '': // In Queue
                // serial number, batch id, name, description, tags, created on, eta, references, sc batch id, accepted rows, comments, actions
                columns = [
                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.comments, order: order++ },
                    { ...commonColumns.actions, order: order++ }
                ];
                break;

            case 'IN_PROGRESS': // In Progress
                // serial number, batch id, name, description, tags, created on, eta, references, sc batch id, accepted rows, progress, comments
                columns = [

                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.progress_percent, order: order++ },
                    { ...commonColumns.comments, order: order++ }
                ];
                break;

            case 'PROCESSED': // Processed
                // serial number, batch id, name, description, tags, created on, eta, references, sc batch id, accepted rows, comments, actions
                columns = [

                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.comments, order: order++ },
                    { ...commonColumns.actions, order: order++ }
                ];
                break;

            case 'APPROVED': // Approved
                // serial number, batch id, name, description, tags, created on, eta, references, sc batch id, accepted rows, comments, download
                columns = [

                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.comments, order: order++ },
                    { ...commonColumns.download, order: order++ }
                ];
                break;

            case 'CANCELLED': // Cancelled
                // serial number, batch id, name, description, tags, created on, eta, references, sc batch id, accepted rows, comments, actions
                columns = [
                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.comments, order: order++ },
                    { ...commonColumns.actions, order: order++ }
                ];
                break;

            default: // Fallback to In Queue configuration
                columns = [
                    { ...commonColumns.batch_id, order: order++ },
                    { ...commonColumns.name, order: order++ },
                    { ...commonColumns.description, order: order++ },
                    { ...commonColumns.tags, order: order++ },
                    { ...commonColumns.created_at, order: order++ },
                    { ...commonColumns.eta, order: order++ },
                    { ...commonColumns.references, order: order++ },
                    { ...commonColumns.supplier_connector_batch_id, order: order++ },
                    { ...commonColumns.accepted_rows, order: order++ },
                    { ...commonColumns.comments, order: order++ },
                    { ...commonColumns.actions, order: order++ }
                ];
                break;
        }

        return columns;
    }



    private getCurrentTabStatus(): string {
        if (this.activeStatusTabIndex >= 0 && this.activeStatusTabIndex < this.statusTabs.length) {
            return this.statusTabs[this.activeStatusTabIndex].status;
        }
        return '';
    }

    private transformBatchData(batches: any[]): any[] {
        return batches.map(batch => ({
            ...batch,
            // Ensure all required fields exist with default values
            name: batch.name || 'Unnamed Batch',
            batch_id: batch.batch_id || '',
            status: batch.status || 'Unknown',
            total_rows: batch.total_rows || 0,
            accepted: batch.accepted || 0,
            accepted_percent: batch.accepted_percent || 0,
            insufficient_rows: batch.insufficient_rows || 0,
            progress_percent: batch.progress_percent || 0,
            eta: batch.eta || null,
            created_at: batch.created_at || null,
            created_by: batch.created_by || 'Unknown',
            description: batch.description || '',
            labels: batch.labels || [],
            has_comments: batch.has_comments || false,
            supplier_connector_batch_id: batch.supplier_connector_batch_id || batch.batch_id || '',
                        references: batch.reference_batch_id && batch.reference_batch_id.length > 0
                ? batch.reference_batch_id[0]
                : '',
            // Add computed fields
            accepted_rows: batch.accepted || 0,
            tags: batch.labels || [],
            comments: batch.has_comments ? [{ text: 'It is a long established fact that a read...' }] : [],
            uploaded_by: batch.created_by || 'Unknown',
            assignee: batch.created_by || 'Unknown'
        }));
    }



    private loadLabelList() {
        return this.homeService.getLabelList(this.subscriptionId).subscribe({
            next: (response: any) => {
                if (response && response.result) {
                    const tags = response.result.map((label: any) => ({
                        id: label.id,
                        name: label.name,
                        slug: label.slug,
                        bg_colour_code: label.bg_colour_code,
                        text_colour_code: label.text_colour_code,
                        // Add custom style for cax-chip
                        customStyle: {
                            backgroundColor: label.bg_colour_code,
                            color: label.text_colour_code
                        }
                    }));
                    this.tagFilterOptions = tags;
                    this.overlayTags = tags; // Set overlayTags for the tag overlay panel
                    this.filteredTags = [...tags]; // Initialize filtered tags
                } else {
                    console.warn('No tags found in response');
                    this.tagFilterOptions = [];
                    this.overlayTags = [];
                    this.filteredTags = [];
                }
            },
            error: (error: any) => {
                console.error('Error loading label list:', error);
                this.overlayTags = [];
                this.tagFilterOptions = [];
                this.filteredTags = [];
            }
        });
    }

    /**
     * Strip HTML tags from text content
     */
    private stripHtmlTags(text: string): string {
        if (!text) return '';
        // Create a temporary div element to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;
        // Return only the text content without HTML tags
        return tempDiv.textContent || tempDiv.innerText || '';
    }

    onTagFilterChange(event: any): void {
        // selectedTags is already bound to ngModel as array of IDs/slugs
        this.currentPage = 1;
        this.loadBatchList();
    }

    copyBatchId(batchId: string): void {
        navigator.clipboard.writeText(batchId).then(() => {
            this.showSuccessToast('Batch ID copied to clipboard', batchId);
        }).catch(err => {
            console.error('Failed to copy batch ID:', err);
            this.showErrorToast('Failed to copy Batch ID', 'Please try again');
        });
    }

    private loadStats() {
        // Try using getStatusCount instead of getStats
        return this.homeService.getStatusCount(
            this.subscriptionId,
            this.searchText,
            this.startDate,
            this.endDate,
            this.selectedTags
        ).subscribe({
            next: (response) => {
                // Update counts in existing tabs instead of replacing them
                this.updateStatusCounts(response.result);
            },
            error: (error) => {
                console.error('Error loading status counts:', error);

                // Fallback to getStats API
                this.homeService.getStats(this.subscriptionId).subscribe({
                    next: (response) => {
                        this.updateStatusCounts(response.result);
                    },
                    error: (statsError) => {
                        console.error('Error loading stats fallback:', statsError);
                        // Initialize tabs if they don't exist
                        if (!this.statusTabs || this.statusTabs.length === 0) {
                            this.statusTabs = this.getDefaultTabs();
                        }
                    }
                });
            }
        });
    }



    private getDefaultTabs(): any[] {
        return [
            { header: 'In Queue', status: '', count: 0 },
            { header: 'In Progress', status: 'IN_PROGRESS', count: 0 },
            { header: 'Processed', status: 'PROCESSED', count: 0 },
            { header: 'Approved', status: 'APPROVED', count: 0 },
            { header: 'Cancelled', status: 'CANCELLED', count: 0 }
        ];
    }

    onStatusTabChange(event: any) {
        if (!this.statusTabs || this.statusTabs.length === 0) {
            return;
        }

        // Handle different event formats
        let newIndex = event?.index !== undefined ? event.index : event;
        this.switchToTab(newIndex);
    }

    // Separate method to handle tab switching logic
    switchToTab(newIndex: number) {
        if (newIndex < 0 || newIndex >= this.statusTabs.length) {
            return;
        }

        // Prevent rapid tab switching while loading
        if (this.isLoadingBatches) {
            console.log('Tab switching blocked - batch list is currently loading');
            return;
        }

        console.log(`Switching to tab ${newIndex} with status: ${this.statusTabs[newIndex]?.status}`);

        this.activeStatusTabIndex = newIndex;
        this.currentStatus = this.statusTabs[newIndex]?.status || '';
        this.currentPage = 1; // Reset to first page when changing tabs

        // Refresh table columns based on new tab
        this.initializeTableColumns();

        // Force change detection
        this.cdr.detectChanges();

        // Load batch list for the new tab
        this.loadBatchList();
    }

    /**
     * Update the counts for each status tab based on the API response
     */
    private updateStatusCounts(counts: any) {
        if (counts && this.statusTabs && this.statusTabs.length > 0) {
            // Try different possible response formats
            let inQueue = 0, inProgress = 0, processed = 0, approved = 0, cancelled = 0;

            // Format 1: Direct properties (in_queue, in_progress, etc.)
            if (counts.in_queue !== undefined) {
                inQueue = counts.in_queue || 0;
                inProgress = counts.in_progress || 0;
                processed = counts.processed || 0;
                approved = counts.approved || 0;
                cancelled = counts.cancelled || 0;
            }
            // Format 2: Status keys with empty string for "In Queue"
            else if (counts[''] !== undefined || counts['In_Progress'] !== undefined) {
                inQueue = counts[''] || 0;
                inProgress = counts['In_Progress'] || 0;
                processed = counts['Processed'] || 0;
                approved = counts['Approved'] || 0;
                cancelled = counts['Cancelled'] || 0;
            }
            // Format 3: Camelcase properties
            else if (counts.inQueue !== undefined) {
                inQueue = counts.inQueue || 0;
                inProgress = counts.inProgress || 0;
                processed = counts.processed || 0;
                approved = counts.approved || 0;
                cancelled = counts.cancelled || 0;
            }

            // Update the tabs
            this.statusTabs[0].count = inQueue;
            this.statusTabs[1].count = inProgress;
            this.statusTabs[2].count = processed;
            this.statusTabs[3].count = approved;
            this.statusTabs[4].count = cancelled;
        }
    }

    onPageChange(event: any) {
        this.currentPage = event.page + 1; // PrimeNG uses 0-based indexing
        this.pageSize = event.rows;
        this.loadBatchList();
    }

    private loadUserNamesToTag() {
        return this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
            next: (response) => {
                this.mentionSuggestions = response.map((user: any) => ({
                    name: user.name,
                    id: `@${user.username}`,
                    type: 'mention'
                }));
            },
            error: (error) => {
                console.error('Error loading user names:', error);
                this.mentionSuggestions = [];
            }
        });
    }

    private loadUserDetails() {
        return this.userService.me(this.subscriptionId).subscribe({
            next: (response) => {
                // Store user details for assignee options
                const currentUser = response.result;
                this.assigneeOptions = [
                    {
                        name: currentUser.name || 'Current User',
                        id: currentUser.id,
                        email: currentUser.email
                    }
                ];
            },
            error: (error) => {
                console.error('Error loading user details:', error);
                this.assigneeOptions = [];
            }
        });
    }

    private loadReviewFilters() {
        return this.reviewService.getReviewFilterList(this.subscriptionId, true).subscribe({
            next: (response) => {
                // Handle review filters - can be used for batch status options
                if (response && response.result) {
                    this.batchStatusList = response.result.map((filter: any) => ({
                        name: filter.name,
                        key: filter.key,
                        icon: filter.icon || 'cax cax-circle',
                        severity: this.mapFilterSeverity(filter.type),
                        child: filter.child || []
                    }));

                    this.statusOptions = this.batchStatusList.map((status: any) => ({
                        label: status.name,
                        value: status.key,
                    }));
                }
            },
            error: (error) => {
                console.error('Error loading review filters:', error);
                this.batchStatusList = [];
                this.statusOptions = [];
            }
        });
    }

    private mapFilterSeverity(type: string): string {
        const severityMap: { [key: string]: string } = {
            'success': 'success',
            'warning': 'warning',
            'error': 'error',
            'info': 'primary'
        };
        return severityMap[type] || 'secondary';
    }

    private mapLabelSeverity(type: string): ChipSeverity {
        const severityMap: { [key: string]: ChipSeverity } = {
            'high': 'error',
            'medium': 'warning',
            'low': 'secondary',
            'info': 'primary'
        };
        return severityMap[type] || 'secondary';
    }

    openBatchDetails(batchData: any) {
        const oldTabIndex = this.dynamicTabs.findIndex(
            tab => tab.data.batchDetails.batch_id === String(batchData.batch_id)
        );
        if (oldTabIndex !== -1) {
            setTimeout(() => {
                this.activeTabIndex = oldTabIndex + 1;
                this.cdr.detectChanges();
            }, 0);
        } else {
            this.dynamicTabs.push({
                header: String(batchData.name),
                component: BatchRowsComponent,
                closable: true,
                data: { batchDetails: batchData },
            });
            this.cdr.detectChanges();
            setTimeout(() => {
                this.activeTabIndex = this.dynamicTabs.length;
                this.cdr.detectChanges();
            }, 0);
        }
    }

    onTabClose(event: { index: number }) {
        const index = event.index;
        this.dynamicTabs.splice(index - 1, 1);
        if (this.dynamicTabs.length) {
            if (index == this.activeTabIndex || index < this.activeTabIndex) {
                this.activeTabIndex = this.activeTabIndex - 1;
                this.cdr.detectChanges();
            }
        } else {
            this.activeTabIndex = 0;
            this.cdr.detectChanges();
        }
    }

    openColumnsList() {
        this.columnSidebarVisible = true;
    }

    // Get all available columns for manage columns functionality
    get allAvailableColumns() {
        const commonColumns = {
            batch_id: { field: 'batch_id', header: 'Batch ID', fixed: true, disabled: true },
            name: { field: 'name', header: 'Name', fixed: true, disabled: true },
            description: { field: 'description', header: 'Description', fixed: false, disabled: false },
            tags: { field: 'tags', header: 'Tags', fixed: false, disabled: false },
            created_at: { field: 'created_at', header: 'Created On', fixed: false, disabled: false },
            eta: { field: 'eta', header: 'ETA', fixed: false, disabled: false },
            references: { field: 'references', header: 'References', fixed: false, disabled: false },
            supplier_connector_batch_id: { field: 'supplier_connector_batch_id', header: 'SC Batch ID', fixed: false, disabled: false },
            accepted_rows: { field: 'accepted_rows', header: 'Accepted Rows', fixed: false, disabled: false },
            progress_percent: { field: 'progress_percent', header: 'Progress', fixed: false, disabled: false },
            comments: { field: 'comments', header: 'Comments', fixed: false, disabled: false },
            actions: { field: 'actions', header: 'Actions', fixed: false, disabled: false },
            download: { field: 'download', header: 'Download', fixed: false, disabled: false },
            created_by: { field: 'created_by', header: 'Created By', fixed: false, disabled: false },
            rejected_rows: { field: 'rejected_rows', header: 'Rejected Rows', fixed: false, disabled: false }
        };

        return Object.values(commonColumns);
    }

    getDownloadMenuItems(rowData: any) {
        return [
            {
                label: 'Download Input',
                icon: 'cax cax-download',
                command: () => this.downloadFile(rowData.batch_id, 'input')
            },
            {
                label: 'Download Output',
                icon: 'cax cax-download',
                command: () => this.checkFileStatus(rowData.batch_id, 'output')
            },
            {
                label: 'Download All',
                icon: 'cax cax-download',
                command: () => this.downloadFile(rowData.batch_id, 'all')
            }
        ];
    }

    getActionDownloadMenuItems(rowData: any) {
        const menuItems = [
            {
                label: 'Output File',
                icon: 'cax cax-document',
                command: () => this.downloadOrGenerateFile(rowData.batch_id, 'output')
            },
            {
                label: 'Assets',
                icon: 'cax cax-folder',
                command: () => this.downloadOrGenerateFile(rowData.batch_id, 'assets')
            }
        ];

        // Add conditional menu items based on row data
        if (rowData.insufficient_rows > 0) {
            menuItems.push({
                label: 'Insufficient Data SKUs',
                icon: 'cax cax-warning',
                command: () => this.downloadOrGenerateFile(rowData.batch_id, 'insufficient')
            });
        }

        if (rowData.duplicate_rows > 0) {
            menuItems.push({
                label: 'Duplicate SKUs',
                icon: 'cax cax-copy',
                command: () => this.downloadOrGenerateFile(rowData.batch_id, 'duplicate')
            });
        }

        if (rowData.other_rows > 0) {
            menuItems.push({
                label: 'Other SKUs',
                icon: 'cax cax-document-text',
                command: () => this.downloadOrGenerateFile(rowData.batch_id, 'other')
            });
        }

        return menuItems;
    }

    approveBatch(batchId: string) {
        this.homeService.statusUpdate(this.subscriptionId, batchId, 'approve').subscribe({
            next: (response) => {
                this.showSuccessToast('Success', 'Batch approved successfully');
                this.loadBatchList(); // Refresh the list
            },
            error: (error) => {
                console.error('Error approving batch:', error);
                this.showErrorToast('Error', 'Failed to approve batch');
            }
        });
    }

    // Delete batch method (from r2e project)
    deleteBatch(batchId: string): void {
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this batch? This action cannot be undone.',
            header: 'Confirm Deletion',
            acceptLabel: 'Yes, Delete',
            rejectLabel: 'Cancel',
            acceptButtonStyleClass: 'cax-button-danger',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            accept: () => {
                this.homeService.deleteBatchList(this.subscriptionId, batchId).subscribe({
                    next: () => {
                        console.log(`Batch ${batchId} deleted successfully`);
                        this.showSuccessToast('Success', 'Batch deleted successfully');
                        this.loadBatchList();
                        this.loadStats();
                    },
                    error: (error) => {
                        console.error(`Error deleting batch ${batchId}:`, error);
                        this.showErrorToast('Error', 'Failed to delete batch');
                    }
                });
            }
        });
    }

    // Enhanced date handling (from r2e project)
    updateBatchETA(batchId: string, eta: Date): void {
        const etaString = eta.toISOString().split('T')[0];
        this.homeService.modifyETA(this.subscriptionId, batchId, etaString).subscribe({
            next: () => {
                console.log(`ETA updated for batch ${batchId}`);
                this.showSuccessToast('Success', 'ETA updated successfully');
                this.loadBatchList();
            },
            error: (error) => {
                console.error(`Error updating ETA for batch ${batchId}:`, error);
                this.showErrorToast('Error', 'Failed to update ETA');
            }
        });
    }

    // Export all functionality (from r2e project)
    exportAllItems(): void {
        this.showSuccessToast('Export', 'File export in progress, Please wait.');

        // Start polling for export status
        timer(1, 3000)
            .pipe(
                switchMap(() => this.homeService.exportBatches(this.subscriptionId)),
                takeUntil(this.stopPolling$)
            )
            .subscribe({
                next: (data) => {
                    this.exportDownloadUrl = data['download_url'];
                    if (this.exportDownloadUrl) {
                        window.open(this.exportDownloadUrl, '_blank');
                        this.stopPolling$.next(true);
                        this.showSuccessToast('Export', 'Export completed successfully');
                    } else {
                        this.showSuccessToast('Export', data['message'] || 'File export in progress, Please wait.');
                    }
                },
                error: (error) => {
                    console.error('Error exporting batches:', error);
                    this.showErrorToast('Error', 'Failed to export batches');
                    this.stopPolling$.next(true);
                }
            });
    }

    get sortedSelectedColumns() {
        return this.batchTableColumns
            .filter((column: any) => column.selected)
            .sort((a: any, b: any) => a.order - b.order);
    }

    filterColumnsList(event: any) {
        this.batchTableColumns = event;
    }

    getBatchStatus(key: string) {
        // Map API status values to display values
        const statusMap: { [key: string]: any } = {
            'In_Progress': {
                name: 'In Progress',
                key: 'In_Progress',
                icon: 'cax cax-clock',
                severity: 'warning',
            },
            'Processed': {
                name: 'Processed',
                key: 'Processed',
                icon: 'cax cax-check-circle',
                severity: 'success',
            },
            'Approved': {
                name: 'Approved',
                key: 'Approved',
                icon: 'cax cax-check-circle',
                severity: 'success',
            },
            'Cancelled': {
                name: 'Cancelled',
                key: 'Cancelled',
                icon: 'cax cax-close-circle',
                severity: 'error',
            },
            'Insufficient_Data': {
                name: 'Insufficient Data',
                key: 'Insufficient_Data',
                icon: 'cax cax-warning',
                severity: 'warning',
            }
        };

        return statusMap[key] || this.batchStatusList.find(status => status.key === key) || {
            name: 'In Queue',
            key: 'in_queue',
            icon: 'cax cax-question-circle',
            severity: 'secondary',
        };
    }

    openBatchStatusPanel(event: any, status: any, rowIndex: number) {
        this.statusPanel.hide();
        setTimeout(() => {
            this.batchStatusRowIndex = rowIndex;
            this.batchStatusPanelList = [];
            this.batchStatusPanelList = this.getBatchFilteredList(status);
            if (this.batchStatusPanelList.length) {
                this.statusPanel.show(event);
            }
        }, 200);
    }

    openBatchLogPanel(event: any, batchData: any) {
        this.batchLogPanel.hide();
        setTimeout(() => {
            this.batchLogList = batchData.audit_log;
            this.batchLogPanel.show(event);
        }, 200);
    }

    getBatchFilteredList(status: string) {
        const parentItem = this.batchStatusList.find((item: any) => item.key === status);
        return parentItem?.child?.length
            ? parentItem.child
                  .map((childKey: any) =>
                      this.batchStatusList.find((item: any) => item.key === childKey)
                  )
                  .filter(Boolean)
            : [];
    }

    updateBatchStatus(status: any) {
        if (status.key === 'cancelled') {
            this.confirmationService.confirm({
                message: 'Are you sure you want to cancel the batch',
                header: 'Are you sure?',
                acceptLabel: 'Yes, cancel batch',
                rejectLabel: 'Cancel',
                acceptButtonStyleClass: 'cax-button-danger',
                rejectButtonStyleClass:
                    'cax-button-secondary cax-button-outlined',
                closable: true,
                headerIcon: 'cax cax-close-circle',
                headerIconStyle: { color: 'var(--error-500)' },
                accept: () => {
                    this.batchList[this.batchStatusRowIndex].status =
                        status.key;
                },
            });
        } else if (status.key === 'approved') {
            this.confirmationService.confirm({
                message:
                    "Are you sure you want to change status to 'Approved'?",
                header: 'Are you sure?',
                acceptLabel: 'Yes',
                rejectLabel: 'Cancel',
                rejectButtonStyleClass:
                    'cax-button-secondary cax-button-outlined',
                closable: true,
                headerIcon: 'cax cax-info-circle',
                headerIconStyle: { color: 'var(--primary-500)' },
                accept: () => {
                    this.batchList[this.batchStatusRowIndex].status =
                        status.key;
                },
            });
        } else if (status.key === 'rework') {
            this.reworkDialogVisible = true;
        } else {
            this.batchList[this.batchStatusRowIndex].status = status.key;
        }
        this.statusPanel.hide();
    }

    openComments(rowData: any) {
        this.currentBatchId = rowData.batch_id;
        this.currentBatchName = rowData.name;
        this.currentBatchHeader = `${rowData.name} (${rowData.batch_id})`;
        // Simple approach - only load comments when sidebar opens
        this.loadBatchComments(rowData.batch_id);
        this.commentsSidebarVisible = true;
        this.openComment = true;
    }

    // Alias for backward compatibility
    openCommentsSidebar(rowData: any) {
        this.openComments(rowData);
    }

    onCommentSidebarClose() {
        this.commentsSidebarVisible = false;
        this.openComment = false;
        this.currentBatchId = '';
        this.currentBatchName = '';
        this.currentBatchHeader = '';
        this.currentBatchComments = [];
    }

    private loadBatchComments(batchId: string) {
        // Show loading state
        this.currentBatchComments = [];

        this.commentsService.getCommentsList(
            this.subscriptionId,
            '1', // page
            '50', // size
            'batch', // category
            batchId, // id
            '', // q
            '' // comment_thread
        ).subscribe({
            next: (response) => {
                console.log('Comments API response:', response);
                // Transform API response to match cax-comments expected format
                const apiComments = response.result || [];

                // Filter out comments with 'None' text as they seem to be placeholder comments
                const validComments = apiComments.filter((comment: any) =>
                    comment.text && comment.text !== 'None' && comment.text.trim() !== ''
                );

                this.currentBatchComments = validComments.map((comment: any) => ({
                    id: comment.comment_id,
                    text: comment.text,
                    rawText: comment.text,
                    date: new Date(comment.created_at),
                    sender: comment.created_by,
                    username: comment.created_by_username,
                    profilePicture: comment.profile_picture,
                    isAdmin: true, // You can adjust this based on your logic
                    isResolved: comment.is_resolved,
                    taggedUsers: comment.tagged_users || [],
                    attachment: comment.attachment || {}
                }));

                // Update comment data in batch for table display
                const currentBatch = this.batchList.find(batch => batch.batch_id === batchId);
                if (currentBatch) {
                    currentBatch.comment_count = validComments.length;
                    currentBatch.has_comments = validComments.length > 0;

                    // Store last comment text and profile picture for table display
                    if (validComments.length > 0) {
                        const lastComment = validComments[0]; // API returns newest first
                        currentBatch.last_comment_text = this.stripHtmlTags(lastComment.text);
                        currentBatch.last_comment_profile_picture = lastComment.profile_picture;
                        currentBatch.last_comment_user = lastComment.created_by;
                    }
                }

                console.log('Processed comments:', this.currentBatchComments);
            },
            error: (error) => {
                console.error('Error loading comments:', error);
                this.currentBatchComments = [];
                this.showErrorToast('Error Loading Comments', 'Failed to load comments for this batch');
            }
        });
    }

    onCommentAdded(comment: any) {
        console.log('Adding comment:', comment);

        // Validate comment data
        if (!comment || !comment.text || !comment.text.trim()) {
            this.showErrorToast('Invalid Comment', 'Comment text cannot be empty');
            return;
        }

        // Post comment via API with proper structure
        const commentData = {
            text: comment.text.trim(),
            mentions: comment.mentions || [],
            hashtags: comment.hashtags || [],
            attachment: comment.attachment || {}
        };

        console.log('Posting comment data:', commentData);

        this.commentsService.postComment(
            this.subscriptionId,
            'batch',
            this.currentBatchId,
            commentData
        ).subscribe({
            next: (response) => {
                console.log('Comment created successfully:', response);

                // Show success message
                this.showSuccessToast('Comment Added', 'Your comment has been posted successfully');

                // Update comment count in the current batch data
                const currentBatch = this.batchList.find(batch => batch.batch_id === this.currentBatchId);
                if (currentBatch) {
                    currentBatch.has_comments = true;
                    currentBatch.comment_count = (currentBatch.comment_count || 0) + 1;
                }

                // Reload comments to get the updated list with a slight delay
                setTimeout(() => {
                    this.loadBatchComments(this.currentBatchId);
                }, 1000);
            },
            error: (error) => {
                console.error('Error posting comment:', error);
                this.showErrorToast('Failed to Post Comment', 'Please try again later');
            }
        });
    }

    openUploadSidebar() {
        this.uploadSidebarVisible = true;
    }

    onUploadComplete(event: any) {
        if (event.success) {
            console.log('Batch uploaded successfully:', event.data);
            // Refresh the batch list to show the new batch
            this.loadBatchList();
            this.loadStats();
            // Close the sidebar
            this.uploadSidebarVisible = false;
            // Show success message
            console.log(event.message);
        } else {
            console.error('Upload failed:', event.message);
            this.showErrorMessage(event.message || 'Failed to upload batch');
        }
    }

    onRowHeightChange(event: 'sm' | 'md' | 'lg' | 'xl' ) {
        this.batchRowSize = event;
        this.avatarSize = event;
        this.inputSize = event === 'xl' ? 'lg' : event;
        this.size = event === 'xl' ? 'lg' : event;

        const buttonSizeMap: { [key in 'sm' | 'md' | 'lg' | 'xl']: 'small' | 'medium' | 'large' } = {
        sm: 'small',
        md: 'medium',
        lg: 'large',
        xl: 'large', // xl maps to large as well
    };

    this.buttonSize = buttonSizeMap[event];

    }

    onFontSizeChange(event: 'sm' | 'md' | 'lg' | 'xl') {
        this.batchFontSize = event;
    }

    onTableSelectionChange(event: any) {
        this.selectedBatches = event;
        this.selectionSidebarVisible = this.selectedBatches.length > 0;

        // Clear input fields when selection changes
        if (this.selectedBatches.length > 0) {
            this.selectedBatchStatus = '';
            this.selectedBatchETA = '';
            this.selectedBatchAssignee = '';
            this.selectedBatchesComment = '';
        }
    }

    onTableSort(event: any) {
        console.log(event);
    }

    onTableFilter(event: any) {
        console.log(event);
        this.batchTableFilters = event.filters;
    }

    get appliedTableFilters() {
        return Object.fromEntries(
            Object.entries(this.batchTableFilters).filter(
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                ([key, value]: any) => value.matchMode !== null
            )
        );
    }

    onMultipleCommentsAdded(comment: string) {
        if (!comment || !comment.trim()) {
            this.showErrorToast('Invalid Comment', 'Comment text cannot be empty');
            return;
        }

        if (this.selectedBatches.length === 0) {
            this.showErrorToast('No Batches Selected', 'Please select batches to add comments');
            return;
        }

        const commentData = {
            text: comment.trim(),
            mentions: [],
            hashtags: []
        };

        let successCount = 0;
        let failureCount = 0;
        const totalBatches = this.selectedBatches.length;

        // Show progress toast
        this.showInfoToast('Adding Comments', `Adding comment to ${totalBatches} batches...`);

        // Post comments to all selected batches via API
        this.selectedBatches.forEach(batch => {
            this.commentsService.postComment(
                this.subscriptionId,
                'batch',
                batch.batch_id,
                commentData
            ).subscribe({
                next: () => {
                    successCount++;
                    console.log(`Comment added to batch ${batch.batch_id}`);

                    // Check if all requests are completed
                    if (successCount + failureCount === totalBatches) {
                        this.handleMultipleCommentsResult(successCount, failureCount, totalBatches);
                    }
                },
                error: (error) => {
                    failureCount++;
                    console.error(`Error adding comment to batch ${batch.batch_id}:`, error);

                    // Check if all requests are completed
                    if (successCount + failureCount === totalBatches) {
                        this.handleMultipleCommentsResult(successCount, failureCount, totalBatches);
                    }
                }
            });
        });

        // Clear the comment input
        this.selectedBatchesComment = '';
    }

    private handleMultipleCommentsResult(successCount: number, failureCount: number, totalBatches: number) {
        // Show result toast
        if (failureCount === 0) {
            this.showSuccessToast('Comments Added', `Successfully added comments to all ${totalBatches} batches`);
        } else if (successCount > 0) {
            this.showWarningToast('Partial Success', `Comments added to ${successCount} batches, ${failureCount} failed`);
        } else {
            this.showErrorToast('Failed to Add Comments', `Failed to add comments to all ${totalBatches} batches`);
        }

        // Reload batch list after a short delay
        setTimeout(() => {
            this.loadBatchList();
        }, 1000);
    }

    updateMultipleBatchStatus(status: string) {
        if (!status) return;

        // Update status via API for all selected batches
        this.selectedBatches.forEach(batch => {
            this.homeService.statusUpdate(
                this.subscriptionId,
                batch.batch_id,
                status
            ).subscribe({
                next: () => {
                    console.log(`Status updated for batch ${batch.batch_id}`);
                },
                error: (error) => {
                    console.error(`Error updating status for batch ${batch.batch_id}:`, error);
                }
            });
        });

        // Reload batch list after a short delay
        setTimeout(() => {
            this.loadBatchList();
        }, 1000);
    }

    updateMultipleBatchETA(eta: string) {
        if (!eta) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.batchList.findIndex(
                item => item.batch_id === batch.batch_id
            );

            if (batchIndex !== -1) {
                this.batchList[batchIndex].eta = eta;
            }
        });

        // Clear the input field after update
        this.selectedBatchETA = '';
    }

    updateMultipleBatchAssignee(assignee: string) {
        if (!assignee) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.batchList.findIndex(
                item => item.batch_id === batch.batch_id
            );

            if (batchIndex !== -1) {
                this.batchList[batchIndex].assignee = assignee;
            }
        });

        // Clear the input field after update
        this.selectedBatchAssignee = '';
    }

    isSaveEnabled(): boolean {
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasAssigneeChange = !!this.selectedBatchAssignee;
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        return hasStatusChange || hasAssigneeChange || hasCommentChange;
    }

    getBatchesToUncheck(): string[] {
        return this.selectedBatches.map(batch => batch.batch_id);
    }

    saveAllChanges() {
        // Check if any changes have been made
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasETAChange = !!this.selectedBatchETA;
        const hasAssigneeChange = !!this.selectedBatchAssignee;
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        if (
            !hasStatusChange &&
            !hasETAChange &&
            !hasAssigneeChange &&
            !hasCommentChange
        ) {
            return;
        }
        const batchesToUncheck = this.getBatchesToUncheck();

        // Show confirmation dialog
        this.confirmationService.confirm({
            message: `Are you sure you want to apply changes to ${this.selectedBatches.length} selected batches?`,
            header: 'Confirm Changes',
            acceptLabel: 'Yes, Save Changes',
            rejectLabel: 'Cancel',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            closable: true,
            headerIcon: 'cax cax-info-circle',
            headerIconStyle: { color: 'var(--primary-500)' },
            accept: () => {
                // Apply all changes
                if (hasStatusChange) {
                    this.updateMultipleBatchStatus(this.selectedBatchStatus);
                }

                if (hasETAChange) {
                    this.updateMultipleBatchETA(this.selectedBatchETA);
                }

                if (hasAssigneeChange) {
                    this.updateMultipleBatchAssignee(
                        this.selectedBatchAssignee
                    );
                }

                if (hasCommentChange) {
                    this.onMultipleCommentsAdded(this.selectedBatchesComment);
                }
                this.selectedBatchStatus = '';
                this.selectedBatchETA = '';
                this.selectedBatchAssignee = '';
                this.selectedBatchesComment = '';
                console.log('Changes saved successfully!');
                this.uncheckSelectedItems(batchesToUncheck);
                this.selectedBatches = [];
                this.selectionSidebarVisible = false;
            },
        });
    }

    uncheckSelectedItems(batchIds: string[]) {
        this.selectedBatches = this.selectedBatches.filter(
            batch => !batchIds.includes(batch.batch_id)
        );

        // Hide selection sidebar if no items remain selected
        if (this.selectedBatches.length === 0) {
            this.selectionSidebarVisible = false;
        }
    }

    handleTagRemove(event: MouseEvent, rowData: any, tag: any): void {
        event.stopPropagation();

        // Show confirmation dialog
        this.confirmationService.confirm({
            message: `Are you sure you want to remove the tag "${tag.name}" from this batch?`,
            header: 'Remove Tag',
            acceptLabel: 'Yes, Remove',
            rejectLabel: 'Cancel',
            acceptButtonStyleClass: 'cax-button-danger',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            headerIcon: 'cax cax-trash',
            headerIconStyle: { color: 'var(--error-500)' },
            accept: () => {
                // Show loading toast
                this.showInfoToast('Removing Tag', 'Removing tag from batch...');

                // Call API to remove tag
                this.homeService.addLabel(
                    this.subscriptionId,
                    rowData.batch_id,
                    '', // add_label_id (empty for removing)
                    tag.id || tag.name // remove_label_id
                ).subscribe({
                    next: () => {
                        // Remove tag from local data immediately for better UX
                        const tagIndex = rowData.labels?.findIndex((t: any) => t.name === tag.name);
                        if (tagIndex !== -1 && rowData.labels) {
                            rowData.labels.splice(tagIndex, 1);
                        }

                        this.showSuccessToast('Tag Removed', `Tag "${tag.name}" has been removed from the batch`);

                        // Reload batch list to sync with server
                        setTimeout(() => {
                            this.loadBatchList();
                        }, 500);
                    },
                    error: (error) => {
                        console.error('Error removing tag from batch:', error);
                        this.showErrorToast('Failed to Remove Tag', 'Please try again later');
                    }
                });
            }
        });
    }

    onTagSearch(searchTerm: string): void {
        if (!searchTerm || searchTerm.trim() === '') {
            this.filteredTags = [...this.overlayTags];
        } else {
             const query = searchTerm.toLowerCase().trim();
            this.filteredTags = this.overlayTags.filter(tag =>
                tag.name.toLowerCase().includes(query)
            );
        }
    }

    handleOverlay(event: MouseEvent, rowData: any, _tag: any) {
    this.selectedRowForTags = rowData;
    this.filteredTags = [...this.overlayTags];
    this.tagsName = '';

    this.tagsPanel.toggle(event);


    setTimeout(() => {
        const overlayEl = this.tagsPanel?.container;

        if (overlayEl) {
            if (this.overlayTags.length === 0) {
                overlayEl.classList.add('no-tags-overlay-bg');
            } else {
                overlayEl.classList.remove('no-tags-overlay-bg');
            }
        }
    }, 0);
}

    updateTag(selectedTag: any): void {
        if (!this.selectedRowForTags) return;

        // Check if tag is already added to avoid duplicates
        const existingTag = this.selectedRowForTags.labels?.find((label: any) =>
            label.name === selectedTag.name || label.id === selectedTag.id
        );

        if (existingTag) {
            this.showInfoToast('Tag Already Added', `Tag "${selectedTag.name}" is already added to this batch`);
            this.tagsPanel.hide();
            return;
        }

        // Show loading toast
        this.showInfoToast('Adding Tag', `Adding tag "${selectedTag.name}" to batch...`);

        // Use API to add label to batch - following r2e pattern
        this.homeService.addLabel(
            this.subscriptionId,
            this.selectedRowForTags.batch_id,
            selectedTag.id || selectedTag.name,
            '' // remove_label_id (empty for adding)
        ).subscribe({
            next: () => {
                console.log(`Tag added to batch ${this.selectedRowForTags.batch_id}`);

                // Add tag to local data immediately for better UX
                if (!this.selectedRowForTags.labels) {
                    this.selectedRowForTags.labels = [];
                }
                this.selectedRowForTags.labels.push({
                    id: selectedTag.id,
                    name: selectedTag.name,
                    slug: selectedTag.slug,
                    bg_colour_code: selectedTag.bg_colour_code,
                    text_colour_code: selectedTag.text_colour_code,
                    severity: selectedTag.severity || 'secondary'
                });

                // Show success toast
                this.showSuccessToast('Tag Added', `Tag "${selectedTag.name}" has been added to the batch`);

                // Reload batch list to sync with server
                setTimeout(() => {
                    this.loadBatchList();
                }, 500);
            },
            error: (error) => {
                console.error('Error adding tag to batch:', error);
                this.showErrorToast('Failed to Add Tag', 'Please try again later');
            }
        });

        // Reset search and hide panel
        this.tagsName = '';
        this.filteredTags = [...this.overlayTags];
        this.tagsPanel.hide();
    }

    openCreateTagSidebar(){
        this.isCreateTagSidebarOpen = true;
        this.tagsPanel.hide();
    }

    onCreateNewTag(tagData: any): void {
        console.log('Creating new tag:', tagData);

        // Show loading toast
        this.showInfoToast('Creating Tag', `Creating tag "${tagData.name}"...`);

        // Use the correct API structure for creating labels
        this.homeService.createLabel(
            this.subscriptionId,
            tagData.name,
            tagData.name, // title parameter
            tagData.backgroundColor || '#007bff',
            tagData.textColor || '#ffffff'
        ).subscribe({
            next: (response) => {
                console.log('New tag created successfully:', response);

                // Show success toast
                this.showSuccessToast('Tag Created', `Tag "${tagData.name}" has been created successfully`);

                // Reload label list to get the new tag and update overlayTags
                this.loadLabelList().add(() => {
                    // Update filteredTags as well
                    this.filteredTags = [...this.overlayTags];
                });

                // Close the sidebar
                this.isCreateTagSidebarOpen = false;
            },
            error: (error) => {
                console.error('Error creating tag:', error);
                this.showErrorToast('Failed to Create Tag', 'Please try again later');
            }
        });
    }

    search(event: any) {
        const query = event.query.toLowerCase();

        if (!query) {
            this.filteredItems = [];
            return;
        }

        // Call API to get matching batches
        this.homeService.getBatchList(
            '1', // page
            '50', // pageSize (or larger if needed)
            '', // status
            query, // searchText
            '', // startDate
            '', // endDate
            this.subscriptionId,
            [] // selectedTags
        ).subscribe({
            next: (response: any) => {
                const batchMatches = (response.result || []).map((batch: any) => ({
                    title: batch.name,
                    batchId: batch.batch_id
                }));
                this.filteredItems = batchMatches.length > 0 ? [
                    {
                        label: 'Batches',
                        items: batchMatches
                    }
                ] : [];
            },
            error: (error: any) => {
                console.error('Error searching batches:', error);
                this.filteredItems = [];
            }
        });
    }

    onItemSelect(event: any) {
        const selected = event?.value;
        if (selected && selected.batchId) {
            // Set search text to the selected batch ID for filtering
            this.searchText = selected.batchId;
            // Reload the batch list to update the table data
            this.applyFilters();
        }
    }

    onDateClick(event: Event, rowData: any): void {
        // Initialize with today's date if no date is present
        this.previousDate = rowData.eta ? new Date(rowData.eta) : null;
        this.selectedDate = rowData.eta ? new Date(rowData.eta) : new Date();
        this.currentRowData = rowData; // Store reference to the current row
        this.calendalPanel.toggle(event);
    }
    handleDateSaved(): void {
        if (this.currentRowData && this.selectedDate) {
            // Update ETA via API
            this.updateBatchETA(this.currentRowData.batch_id, this.selectedDate);
        }
        this.calendalPanel.hide();
    }
    // Handle date cancel
    handleDateCancel(): void {
        this.selectedDate = this.previousDate;
        this.calendalPanel.hide();
    }

    onStatusChange(row: any) {
        console.log('Status changed:', row.status);
        // Update status via API
        this.homeService.statusUpdate(
            this.subscriptionId,
            row.batch_id,
            row.status
        ).subscribe({
            next: () => {
                console.log(`Status updated for batch ${row.batch_id}`);
            },
            error: (error) => {
                console.error(`Error updating status for batch ${row.batch_id}:`, error);
            }
        });
    }

    onDescriptionChange(row: any) {
        console.log('Description updated:', row.description);
        // Persist if necessary - implement API call if available
    }



    // User permissions
    private loadUserPermissions(): void {
        this.userService.me(this.subscriptionId).subscribe({
            next: (response) => {
                // Set permissions based on user role
                this.userPermissions = response.result;
                this.canApprove = this.userPermissions.role === 'admin' || this.userPermissions.role === 'approver';
                this.canDelete = this.userPermissions.role === 'admin';
                this.canEdit = this.userPermissions.role === 'admin' || this.userPermissions.role === 'editor';
                this.canDownload = true; // Most users can download
            },
            error: (error) => {
                console.error('Error loading user permissions:', error);
                // Set default permissions
                this.canApprove = false;
                this.canDelete = false;
                this.canEdit = false;
                this.canDownload = true;
            }
        });
    }

    // Template management
    private loadTemplates(): void {
        this.homeService.getTemplateId(this.subscriptionId, 'all').subscribe({
            next: (response) => {
                this.availableTemplates = response.result || [];
            },
            error: (error) => {
                console.error('Error loading templates:', error);
                this.availableTemplates = [];
            }
        });
    }

    // Enhanced filtering
    applyFilters(): void {
        this.currentPage = 1; // Reset to first page
        this.loadBatchList();
        this.loadStats(); // Refresh status counts
    }

    clearFilters(): void {
        this.searchText = '';
        this.selectedItem = undefined; // Clear search bar selection
        this.filteredItems = []; // Clear search suggestions
        this.startDate = '';
        this.endDate = '';
        this.selectedTags = [];
        this.assignedTo = '';
        this.priority = [];
        this.applyFilters();
        this.loadBatchList(); // Call list API after clearing search
    }

    // File operations
    downloadFile(batchId: string, fileType: string): void {
        this.fileDownloadProgress[batchId] = 0;

        this.homeService.downloadFile(this.subscriptionId, batchId, fileType).subscribe({
            next: (response) => {
                // Create download link
                const blob = new Blob([response], { type: 'application/octet-stream' });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${batchId}_${fileType}.csv`;
                link.click();
                window.URL.revokeObjectURL(url);

                this.fileDownloadProgress[batchId] = 100;
                setTimeout(() => {
                    delete this.fileDownloadProgress[batchId];
                }, 2000);
            },
            error: (error) => {
                console.error('Error downloading file:', error);
                this.showErrorMessage('Failed to download file');
                delete this.fileDownloadProgress[batchId];
            }
        });
    }

    generateFile(batchId: string, bucket: string): void {
        this.fileGenerationStatus[batchId] = 'generating';

        this.homeService.generateBucketFile(this.subscriptionId, batchId, bucket).subscribe({
            next: (response) => {
                this.fileGenerationStatus[batchId] = 'completed';
                setTimeout(() => {
                    delete this.fileGenerationStatus[batchId];
                }, 3000);
            },
            error: (error) => {
                console.error('Error generating file:', error);
                this.fileGenerationStatus[batchId] = 'failed';
                this.showErrorMessage('Failed to generate file');
                setTimeout(() => {
                    delete this.fileGenerationStatus[batchId];
                }, 3000);
            }
        });
    }

    checkFileStatus(batchId: string, fileType: string): void {
        this.homeService.getSingleBatchList(this.subscriptionId, batchId, fileType).subscribe({
            next: (response) => {
                const status = response.result?.status;
                if (status === 'ready') {
                    this.downloadFile(batchId, fileType);
                } else if (status === 'processing') {
                    this.fileGenerationStatus[batchId] = 'processing';
                } else {
                    this.generateFile(batchId, fileType);
                }
            },
            error: (error) => {
                console.error('Error checking file status:', error);
                this.showErrorMessage('Failed to check file status');
            }
        });
    }



    // Bulk actions
    performBulkAction(action: string): void {
        if (this.selectedBatches.length === 0) return;

        this.bulkActionInProgress = true;
        const batchIds = this.selectedBatches.map(batch => batch.batch_id);

        switch (action) {
            case 'approve':
                this.confirmationService.confirm({
                    message: `Are you sure you want to approve ${this.selectedBatches.length} batches?`,
                    header: 'Confirm Bulk Approval',
                    acceptLabel: 'Yes, Approve All',
                    rejectLabel: 'Cancel',
                    acceptButtonStyleClass: 'cax-button-success',
                    rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
                    accept: () => {
                        this.executeBulkAction(batchIds, 'approved');
                    }
                });
                break;
            case 'cancel':
                this.confirmationService.confirm({
                    message: `Are you sure you want to cancel ${this.selectedBatches.length} batches?`,
                    header: 'Confirm Bulk Cancellation',
                    acceptLabel: 'Yes, Cancel All',
                    rejectLabel: 'Cancel',
                    acceptButtonStyleClass: 'cax-button-danger',
                    rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
                    accept: () => {
                        this.executeBulkAction(batchIds, 'cancelled');
                    }
                });
                break;
            case 'delete':
                this.confirmationService.confirm({
                    message: `Are you sure you want to delete ${this.selectedBatches.length} batches? This action cannot be undone.`,
                    header: 'Confirm Bulk Deletion',
                    acceptLabel: 'Yes, Delete All',
                    rejectLabel: 'Cancel',
                    acceptButtonStyleClass: 'cax-button-danger',
                    rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
                    accept: () => {
                        this.executeBulkAction(batchIds, 'delete');
                    }
                });
                break;
            case 'download':
                batchIds.forEach(batchId => {
                    this.downloadFile(batchId, 'output');
                });
                break;
        }
    }

    private executeBulkAction(batchIds: string[], action: string): void {
        const totalBatches = batchIds.length;
        let completedCount = 0;
        let failedCount = 0;
        const failedBatches: string[] = [];

        // Show progress toast
        this.showInfoToast('Bulk Action Started', `Processing ${totalBatches} batches...`);

        const promises = batchIds.map(batchId => {
            if (action === 'delete') {
                return this.homeService.deleteBatchList(this.subscriptionId, batchId).toPromise()
                    .then(() => {
                        completedCount++;
                        return { success: true, batchId };
                    })
                    .catch(error => {
                        failedCount++;
                        failedBatches.push(batchId);
                        return { success: false, batchId, error };
                    });
            } else {
                return this.homeService.statusUpdate(this.subscriptionId, batchId, action).toPromise()
                    .then(() => {
                        completedCount++;
                        return { success: true, batchId };
                    })
                    .catch(error => {
                        failedCount++;
                        failedBatches.push(batchId);
                        return { success: false, batchId, error };
                    });
            }
        });

        Promise.allSettled(promises).then((results) => {
            this.bulkActionInProgress = false;
            this.selectedBatches = [];
            this.selectionSidebarVisible = false;

            // Show results
            if (failedCount === 0) {
                this.showSuccessToast('Bulk Action Completed', `Successfully processed all ${totalBatches} batches`);
            } else if (completedCount > 0) {
                this.showWarningToast('Bulk Action Partially Completed',
                    `${completedCount} batches processed successfully, ${failedCount} failed`);
            } else {
                this.showErrorToast('Bulk Action Failed', `All ${totalBatches} batches failed to process`);
            }

            // Refresh data regardless of results
            this.loadBatchList();
            this.loadStats();
        }).catch(error => {
            console.error('Error performing bulk action:', error);
            this.bulkActionInProgress = false;
            this.showErrorToast('Bulk Action Error', 'An unexpected error occurred during bulk processing');
        });
    }

    // Error handling
    private showErrorMessage(message: string): void {
        this.errorMessage = message;
        this.showError = true;
        setTimeout(() => {
            this.showError = false;
            this.errorMessage = '';
        }, 5000);
        // Also show toast notification
        this.showErrorToast('Error', message);
    }

    // Success message handling
    private showSuccessMessage(message: string): void {
        console.log('Success:', message);
        this.showSuccessToast('Success', message);
    }

    // Toast notification methods
    private showSuccessToast(summary: string, detail: string): void {
        this.messageService.add({
            severity: 'success',
            summary: summary,
            detail: detail,
            life: 3000
        });
    }

    private showErrorToast(summary: string, detail: string): void {
        this.messageService.add({
            severity: 'error',
            summary: summary,
            detail: detail,
            life: 5000
        });
    }

    private showInfoToast(summary: string, detail: string): void {
        this.messageService.add({
            severity: 'info',
            summary: summary,
            detail: detail,
            life: 3000
        });
    }

    private showWarningToast(summary: string, detail: string): void {
        this.messageService.add({
            severity: 'warn',
            summary: summary,
            detail: detail,
            life: 4000
        });
    }



    // Enhanced status update with confirmation
    updateBatchStatusWithConfirmation(batchId: string, newStatus: string, comment?: string): void {
        const statusName = this.getBatchStatus(newStatus).name;

        this.confirmationService.confirm({
            message: `Are you sure you want to change the status to "${statusName}"?`,
            header: 'Confirm Status Change',
            acceptLabel: 'Yes, Update Status',
            rejectLabel: 'Cancel',
            acceptButtonStyleClass: 'cax-button-primary',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            accept: () => {
                this.homeService.statusUpdate(this.subscriptionId, batchId, newStatus).subscribe({
                    next: () => {
                        console.log(`Status updated for batch ${batchId}`);
                        this.loadBatchList();
                        this.loadStats();
                    },
                    error: (error) => {
                        console.error(`Error updating status for batch ${batchId}:`, error);
                        this.showErrorMessage('Failed to update status');
                    }
                });
            }
        });
    }

    // Enhanced tag management
    createNewTag(tagData: any): void {
        this.homeService.createLabel(
            this.subscriptionId,
            tagData.name,
            tagData.description || tagData.name,
            tagData.backgroundColor || '#007bff',
            tagData.textColor || '#ffffff'
        ).subscribe({
            next: () => {
                console.log('New tag created successfully');
                this.loadLabelList();
                this.isCreateTagSidebarOpen = false;
            },
            error: (error) => {
                console.error('Error creating tag:', error);
                this.showErrorMessage('Failed to create tag');
            }
        });
    }

    removeTagFromBatch(batchId: string, tagId: string): void {
        this.homeService.addLabel(this.subscriptionId, batchId, '', tagId).subscribe({
            next: () => {
                console.log(`Tag removed from batch ${batchId}`);
                this.loadBatchList();
            },
            error: (error) => {
                console.error('Error removing tag:', error);
                this.showErrorMessage('Failed to remove tag');
            }
        });
    }

    // Enhanced search with debouncing
    private searchTimeout: any;
    onSearchChange(searchTerm: string): void {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.searchText = searchTerm;
            this.applyFilters();
        }, 500);
    }

    // Enhanced pagination with retry
    onPageChangeWithRetry(event: any): void {
        this.currentPage = event.page + 1;
        this.pageSize = event.rows;

        // Retry logic for pagination
        let retryCount = 0;
        const maxRetries = 3;

        const attemptLoad = () => {
            this.loadBatchList();
            // Note: loadBatchList already handles errors internally
        };

        attemptLoad();
    }

    // Enhanced batch details with audit trail
    openBatchDetailsWithAudit(batchData: any): void {
        // Load audit trail
        this.homeService.getLog(this.subscriptionId, 'batch', batchData.batch_id).subscribe({
            next: (response) => {
                batchData.audit_trail = response.result || [];
                this.openBatchDetails(batchData);
            },
            error: (error) => {
                console.error('Error loading audit trail:', error);
                this.openBatchDetails(batchData);
            }
        });
    }

    // Enhanced comments with file attachments
    onCommentAddedWithAttachment(comment: any): void {
        const commentData = {
            text: comment.text,
            mentions: comment.mentions || [],
            hashtags: comment.hashtags || [],
            attachments: comment.attachments || []
        };

        this.commentsService.postComment(
            this.subscriptionId,
            'batch',
            this.currentBatchId,
            commentData
        ).subscribe({
            next: () => {
                this.loadBatchComments(this.currentBatchId);
            },
            error: (error) => {
                console.error('Error posting comment:', error);
                this.showErrorMessage('Failed to post comment');
            }
        });
    }

    // Enhanced file upload handling
    onFileUpload(file: File): void {
        // Handle file upload for comments or batch attachments
        const formData = new FormData();
        formData.append('file', file);

        // You would implement the actual file upload logic here
        console.log('File upload initiated:', file.name);
    }

    // Enhanced keyboard navigation
    onKeyDown(event: KeyboardEvent, action: string, data?: any): void {
        switch (event.key) {
            case 'Enter':
                event.preventDefault();
                if (action === 'openDetails' && data) {
                    this.openBatchDetails(data);
                } else if (action === 'saveChanges') {
                    this.saveAllChanges();
                }
                break;
            case 'Escape':
                event.preventDefault();
                this.selectionSidebarVisible = false;
                this.commentsSidebarVisible = false;
                this.uploadSidebarVisible = false;
                break;
            case 'Delete':
                event.preventDefault();
                if (this.selectedBatches.length > 0) {
                    this.performBulkAction('delete');
                }
                break;
        }
    }

    // Enhanced accessibility
    getAriaLabel(batch: any): string {
        return `Batch ${batch.name} with status ${this.getBatchStatus(batch.status).name}, ${batch.accepted_rows} of ${batch.total_rows} rows processed`;
    }

    getAriaDescribedBy(batch: any): string {
        return `batch-${batch.batch_id}-description`;
    }

    // Enhanced loading states
    getLoadingMessage(): string {
        if (this.isLoadingBatches) {
            return 'Loading batches...';
        } else if (this.bulkActionInProgress) {
            return 'Processing bulk action...';
        } else if (Object.keys(this.fileDownloadProgress).length > 0) {
            return 'Downloading files...';
        } else if (Object.keys(this.fileGenerationStatus).length > 0) {
            return 'Generating files...';
        }
        return '';
    }

    // Enhanced error recovery
    retryFailedOperation(operation: string, data?: any): void {
        switch (operation) {
            case 'loadBatches':
                this.loadBatchList();
                break;
            case 'loadStats':
                this.loadStats();
                break;
            case 'loadTags':
                this.loadLabelList();
                break;
            case 'downloadFile':
                if (data) {
                    this.downloadFile(data.batchId, data.fileType);
                }
                break;
            case 'generateFile':
                if (data) {
                    this.generateFile(data.batchId, data.bucket);
                }
                break;
        }
    }

        // Handler for search bar clear icon
    onSearchClear() {
        this.searchText = '';
        this.selectedItem = undefined;
        this.filteredItems = [];
        this.currentPage = 1;
        this.loadBatchList();
    }

    // Date range calendar methods
    onDateRangeSelect(event: any): void {
        if (event && event.length === 2) {
            this.startDate = this.formatDate(event[0]);
            this.endDate = this.formatDate(event[1]);
            this.currentPage = 1;
            this.loadBatchList();
        }
    }

    onDateRangeClear(): void {
        this.dateRange = [];
        this.startDate = '';
        this.endDate = '';
        this.currentPage = 1;
        this.loadBatchList();
    }

    private formatDate(date: Date): string {
        if (!date) return '';
        return date.toISOString().split('T')[0];
    }

    // Handler for clearing tag selection
    onTagSelectionClear() {
        this.selectedTags = [];
        this.currentPage = 1;
        this.loadBatchList(); // Reload the batch list without tag filters
    }



    /**
     * Refresh all data
     */
    refreshData() {
        this.loadInitialData();
    }



    /**
     * Download input file for a batch
     */
    downloadInputFile(batchId: string): void {
        if (!this.subscriptionId) {
            this.showErrorMessage('Subscription ID not found');
            return;
        }

        this.homeService.downloadInputFile(this.subscriptionId, batchId).subscribe({
            next: (response) => {
                if (response && response.url) {
                    window.open(response.url, '_blank');
                } else {
                    this.showErrorMessage('Download URL not available');
                }
            },
            error: (error) => {
                console.error('Error downloading input file:', error);
                this.showErrorMessage('Failed to download input file');
            }
        });
    }

    /**
     * Download or generate file based on simplified logic
     */
    downloadOrGenerateFile(batchId: string, bucket: string): void {
        if (!this.subscriptionId) {
            this.showErrorMessage('Subscription ID not found');
            return;
        }

        // Try to download first, if it fails, generate and then download
        this.downloadBucketFile(batchId, bucket);
    }

    /**
     * Generate bucket file (from r2e project)
     */
    private generateBucketFile(batchId: string, bucket: string): void {
        this.fileGenerationStatus[batchId] = 'generating';
        this.toolTipMessage = 'Processing... please wait';

        this.homeService.generateBucketFile(this.subscriptionId, batchId, bucket).subscribe({
            next: (response) => {
                this.fileGenerationStatus[batchId] = 'completed';
                setTimeout(() => {
                    delete this.fileGenerationStatus[batchId];
                    this.downloadBucketFile(batchId, bucket);
                }, 1000);
            },
            error: (error) => {
                console.error('Error generating file:', error);
                this.fileGenerationStatus[batchId] = 'error';
                this.showErrorMessage('Failed to generate file');
                setTimeout(() => {
                    delete this.fileGenerationStatus[batchId];
                }, 3000);
            }
        });
    }

    /**
     * Download file directly with fallback to generation
     */
    private downloadBucketFile(batchId: string, bucket: string): void {
        this.homeService.downloadFile(this.subscriptionId, batchId, bucket).subscribe({
            next: (response) => {
                if (response && response.url) {
                    window.open(response.url, '_blank');
                } else {
                    // If no URL, try to generate the file first
                    this.generateBucketFile(batchId, bucket);
                }
            },
            error: (error) => {
                console.error('Error downloading file, trying to generate:', error);
                // If download fails, try to generate the file first
                this.generateBucketFile(batchId, bucket);
            }
        });
    }
}
