import { Component, OnInit } from '@angular/core';
import { NavigationEnd,ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { LoadingComponent } from './components/loading/loading.component';
import { AsyncPipe, CommonModule } from '@angular/common';
import { AuthService } from '@auth0/auth0-angular';
import { ThemeGeneratorService } from 'cax-design-system/api';
import { NavigationModule } from 'cax-design-system/navigation';
import { filter, map, switchMap } from 'rxjs';
import { UserService } from '../services/user.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, LoadingComponent, CommonModule, AsyncPipe, NavigationModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  providers: [ThemeGeneratorService],
})
export class AppComponent implements OnInit {
  navBarExpanded: boolean = false;
  topNavList: any[] = [];
  bottomNavList: any[] = [];
  activeTab: any = { position: 'top', index: 0 };
  themeCode = '#5946b9';
  userName: string = 'Loading...';
  userEmail: string = '';
  clientLogo: string = '';
  clientName: string = '';
  dashboardUrl: string = '';

  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    public auth: AuthService,
    private themeGenerator: ThemeGeneratorService,
    private userService: UserService
  ) {

    this.themeGenerator.applyTheme(this.themeCode);
  }

  ngOnInit(): void {
    this.initialiseNavList();
    this.handleSubscriptionIdFromUrl();
    this.handleInitialSubscriptionId();
    this.loadUserData();
  }

  private loadUserData(): void {
    const subscriptionId = localStorage.getItem('SubscriptionID');
    if (subscriptionId) {
      this.userService.me(subscriptionId).subscribe({
        next: (response) => {
          console.log('User data loaded:', response);
          if (response && response.result) {
            const userData = response.result;
            this.userName = userData.name || 'User';
            this.userEmail = userData.email || '';
            this.clientLogo = userData.client_logo || '';
            this.clientName = userData.client_name || '';
            this.dashboardUrl = userData.dashboard_url || '/modules';

            console.log('User name set to:', this.userName);
          }
        },
        error: (error) => {
          console.error('Error loading user data:', error);
          this.userName = 'User';
        }
      });
    } else {
      console.warn('No subscription ID found in localStorage');
      this.userName = 'User';
    }
  }

  private handleSubscriptionIdFromUrl(): void {
    this.router.events
        .pipe(
            filter(
                (event): event is NavigationEnd =>
                    event instanceof NavigationEnd
            )
        )
        .subscribe((event: NavigationEnd) => {
            const url = new URL(event.url, window.location.origin);
            const subscriptionId = url.searchParams.get('sub');

            if (subscriptionId) {
                localStorage.setItem('SubscriptionID', subscriptionId);
            }
        });
  }

  private handleInitialSubscriptionId(): void {
    const currentUrl = new URL(window.location.href);
    const subscriptionId = currentUrl.searchParams.get('sub');

    if (subscriptionId) {
        localStorage.setItem('SubscriptionID', subscriptionId);
    }
  }

initialiseNavList() {
    this.topNavList = [
        {
            label: 'Batches',
            icon: 'cax-document-text',
            command: () => {
                const subscriptionId = localStorage.getItem('SubscriptionID');
                const queryParams = subscriptionId ? { sub: subscriptionId } : {};
                this.router.navigate(['home'], { queryParams });
            },
        },
        {
            label: 'Riview',
            icon: 'cax cax-pen-square',
            command: () => {
                this.router.navigate(['/#']);
            },
        },
    ];

    this.bottomNavList = [
        {
            label: 'Settings',
            icon: 'cax cax-settings',
            command: () => {
                console.log('Redirect to apps');
            },
        },
    ];

}

navBarStatus(event: any) {
    this.navBarExpanded = event;
}

changeActiveItem(event: any) {
    this.activeTab = event.activeTab;
}

onLogoClick() {
    console.log('Logo clicked, navigating to dashboard:', this.dashboardUrl);
    if (this.dashboardUrl) {
        // Navigate to the dashboard URL from the API response
        window.location.href = this.dashboardUrl;
    } else {
        // Fallback to default modules page
        const subscriptionId = localStorage.getItem('SubscriptionID');
        const queryParams = subscriptionId ? { sub: subscriptionId } : {};
        this.router.navigate(['/modules'], { queryParams });
    }
}

// openHelpCentre() {
//     const subscriptionId = localStorage.getItem('SubscriptionID');
//     const queryParams = subscriptionId ? { sub: subscriptionId } : {};
//     this.router.navigate(['/#'], { queryParams });
// }
}
