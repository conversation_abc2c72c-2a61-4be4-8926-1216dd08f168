<app-loading *ngIf="auth.isLoading$ | async; else loaded"></app-loading>
<ng-template #loaded>
  <cax-navigation
    (onNavExpanded)="navBarStatus($event)"
    [activeTab]="activeTab"
    [header]="'Content Enrichment'"
    [topNavList]="topNavList"
    [bottomNavList]="bottomNavList"
    [notifications]="false"
    [helpCentre]="false"
    [profile]="false"
    [settings]="false"
    [version]="'V.2.1'"
    [copyrightYear]="2025"
    [userName]="userName"
    [subscriptionMode]="'advance'"
    (onLogoClick)="onLogoClick()"
    (onNavListItemChange)="changeActiveItem($event)"

  />

  <div class="main-content" [ngClass]="{ expanded: navBarExpanded }">
    <router-outlet></router-outlet>
  </div>
</ng-template>
