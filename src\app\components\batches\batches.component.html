<cax-tabView
    mode="segment"
    [pinnedIndexes]="pinnedTabs"
    [(activeIndex)]="activeTabIndex"
    (onClose)="onTabClose($event)"
    [scrollable]="true">
    <cax-tabPanel icon="cax cax-document-text">
        <div class="action-container">

            <!-- Error Message Display -->
            <div *ngIf="showError" class="error-message" role="alert">
                <i class="cax cax-close-circle"></i>
                <span>{{ errorMessage }}</span>
                <button (click)="showError = false" class="error-close-btn">
                    <i class="cax cax-close"></i>
                </button>
            </div>

            <!-- Loading Message -->
            <div *ngIf="getLoadingMessage()" class="loading-message">
                <i class="cax cax-loading"></i>
                <span>{{ getLoadingMessage() }}</span>
            </div>

            <div class="search-container">
                <cax-autoComplete
                    [(ngModel)]="selectedItem"
                    [suggestions]="filteredItems"
                    (completeMethod)="search($event)"
                    field="title"
                    [showClear]="true"
                    [isloading]="isLoading"
                    [style]="{ width: '100%' }"
                    [placeholder]="'Search Batch...'"
                    [group]="true"
                    (onSelect)="onItemSelect($event)"
                    (onClear)="onSearchClear()">
                    <ng-template let-group caxTemplate="group">
                        <div class="autocomplete-group-item">
                            <span>{{ group.label }}</span>
                        </div>
                    </ng-template>
                    <ng-template let-item caxTemplate="item">
                        <div class="autocomplete-item">
                            <div class="item-list">
                                <div class="item-title">
                                    <i class="cax cax-history"></i>
                                    <span>{{ item.title }}</span>
                                </div>
                            </div>
                            <span class="batchId" *ngIf="item.batchId">
                                {{ item.batchId }}
                            </span>
                        </div>
                    </ng-template>
                    <div class="line"></div>
                </cax-autoComplete>

            </div>




            <div class="button-container">

               <!-- Date Range Calendar -->
                <cax-calendar
                    [(ngModel)]="dateRange"
                    [selectionMode]="'range'"
                    [showIcon]="true"
                    [placeholder]="'Select Date Range'"
                    [style]="{ width: '240px', 'margin-left': '12px' }"
                    (onSelect)="onDateRangeSelect($event)"
                    (onClear)="onDateRangeClear()">
                </cax-calendar>


               <!-- Filter by Tags Dropdown -->

                <cax-multiSelect
                    [options]="tagFilterOptions"
                    [(ngModel)]="selectedTags"
                    name="tags"
                    optionLabel="name"
                    optionValue="slug"
                    placeholder="Filter by tags"
                    filterPlaceHolder="Search"
                    [filter]="true"
                    [showClear]="true"
                    display="comma"
                    (onChange)="onTagFilterChange($event)"
                    (onClear)="onTagSelectionClear()"
                    [style]="{ width: '240px' }">
                </cax-multiSelect>


                <!-- Clear Filters Button -->
                <cax-button
                    *ngIf="object.keys(appliedTableFilters)?.length || searchText || selectedTags.length"
                    severity="secondary"
                    icon="cax cax-close"
                    (click)="clearFilters()"
                    [title]="'Clear all filters'"></cax-button>

                <!-- Table Configuration -->
                <!-- <cax-button
                    (click)="tableSizePanel.toggle($event)"
                    severity="secondary"
                    icon="cax cax-text-icon-bold"
                    [title]="'Table settings'"></cax-button> -->

                <!-- Column Management -->
                <cax-button
                    (click)="openColumnsList()"
                    severity="secondary"
                    icon="cax cax-layers"
                    [title]="'Manage columns'"></cax-button>

                    <!-- Refresh Button -->
                <cax-button
                    (click)="refreshData()"
                    severity="secondary"
                    icon="cax cax-refresh"
                    [title]="'Refresh data'"
                    [disabled]="isLoadingBatches"></cax-button>

                <!-- Export Button -->
                <cax-button
                    leftIcon="cax cax-upload"
                    label="Export all"
                    severity="primary"
                    [outlined]="true"
                    (click)="exportAllItems()">
                </cax-button>



                <!-- Upload Button -->
                <cax-button
                    (click)="openUploadSidebar()"
                    leftIcon="cax cax-upload"
                    label="Upload New Batch"
                    [title]="'Upload new batch'"></cax-button>
            </div>

        </div>



        <div class="table-container">
           <!-- Status Tabs -->


        <cax-tabView
            [style]="{ 'margin-bottom' : '10px' }"
            mode="line"
            [(activeIndex)]="activeStatusTabIndex"
            (onTabChange)="onStatusTabChange($event)"
            (activeIndexChange)="onStatusTabChange({index: $event})">
            <cax-tabPanel *ngFor="let tab of statusTabs; let i = index"
                          [header]="tab.header + ' (' + tab.count + ')'">
            </cax-tabPanel>
        </cax-tabView>

            <cax-table
                [columns]="sortedSelectedColumns"
                styleClass="cax-datatable-gridlines"
                [value]="batchList"
                [scrollable]="true"
                [fontSize]="batchFontSize"
                [rowSize]="batchRowSize"
                [filters]="batchTableFilters"
                [editMode]="'cell'"
                filterType="custom"
                sortType="custom"
                sortMode="multiple"
                [(selection)]="selectedBatches"
                (selectionChange)="onTableSelectionChange($event)"
                (onSort)="onTableSort($event)"
                (onFilter)="onTableFilter($event)"
                [loading]="isLoadingBatches">
                <ng-template caxTemplate="header" let-columns>
                    <tr>
                        <th
                            caxColumnWidth
                            [minWidth]="'60px'"
                            [maxWidth]="'60px'"
                            caxFrozenColumn>
                            <cax-tableHeaderCheckbox />
                        </th>
                        <th
                            caxColumnWidth
                            [minWidth]="col.minWidth"
                            [maxWidth]="col.maxWidth"
                            caxFrozenColumn
                            [frozen]="col.fixed"
                            [alignFrozen]="col.position ? col.position : 'left'"
                            *ngFor="let col of columns">
                            {{ col.header }}
                        </th>
                    </tr>
                </ng-template>
                <ng-template
                    caxTemplate="body"
                    let-rowData
                    let-index="rowIndex"
                    let-columns="columns">
                    <tr
                        [attr.aria-label]="getAriaLabel(rowData)"
                        [attr.aria-describedby]="getAriaDescribedBy(rowData)"
                        (keydown)="onKeyDown($event, 'row', rowData)"
                        tabindex="0">
                        <td caxFrozenColumn>
                            <cax-tableCheckbox
                                [index]="index"
                                [value]="rowData" />
                        </td>
                        <td
                            caxFrozenColumn
                            [frozen]="col.fixed"
                            [alignFrozen]="col.position ? col.position : 'left'"
                            *ngFor="let col of columns">
                            <div
                                class="w-100 h-100 data-container"
                                [ngSwitch]="col.field">
                                <!-- case for serial_number -->
                                <div *ngSwitchCase="'serial_number'" class="serial-number-container">
                                    <span>{{ index + 1 + (currentPage - 1) * pageSize }}</span>
                                </div>
                                <!-- case for batch_id -->
                                <div
                                    *ngSwitchCase="'batch_id'"
                                    class="batch-id-container"
                                    style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                    <span class="batch-id-link" (click)="openBatchDetails(rowData)">{{
                                        rowData[col.field]
                                    }}</span>
                                    <i class="cax cax-copy"
                                       style="color: var(--primary-500); cursor: pointer; margin-left: auto;"
                                       (click)="copyBatchId(rowData[col.field])"
                                       title="Copy Batch ID"></i>
                                </div>
                                <!-- case for name -->
                                <div
                                    *ngSwitchCase="'name'"
                                    class="name-container">
                                    <div class="name-section" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                        <span class="batch-name" [title]="rowData.name">{{ rowData.name || 'Unnamed Batch' }}</span>
                                        <i class="cax cax-download download-icon"
                                           [title]="'Download input file'"
                                           (click)="downloadInputFile(rowData.batch_id)"
                                           style="margin-left: auto; cursor: pointer;"></i>
                                    </div>
                                </div>
                                <!-- case for description -->
                                <div
                                    *ngSwitchCase="'description'"
                                    class="description-container">
                                    <span class="batch-description" [title]="rowData.description">{{ rowData.description || 'No description' }}</span>
                                </div>
                                <!-- case for batch status -->
                                <div *ngSwitchCase="'status'">
                                    <cax-chip
                                        (click)="
                                            canEdit ? openBatchStatusPanel(
                                                $event,
                                                rowData[col.field],
                                                index
                                            ) : null
                                        "
                                        [label]="
                                            getBatchStatus(rowData[col.field])
                                                .name
                                        "
                                        [severity]="
                                            getBatchStatus(rowData[col.field])
                                                .severity
                                        "

                                        [size]="size"
                                        [style.cursor]="canEdit ? 'pointer' : 'default'"
                                        [title]="canEdit ? 'Click to change status' : 'Status cannot be changed'"></cax-chip>
                                </div>
                                <!-- case for the eta -->
                                <div
                                    *ngSwitchCase="'eta'"
                                    style="cursor: pointer"
                                    class="w-100 h-100 eta-cell"
                                    (click)="onDateClick($event, rowData)">
                                    <span>
                                        {{
                                            rowData.eta
                                                ? (rowData.eta
                                                  | date: 'dd/MM/yyyy')
                                                : ''
                                        }}
                                    </span>
                                </div>
                                <!-- download column -->
                                <div *ngSwitchCase="'download'" class="download-container">
                                    <cax-splitButton
                                        [icon]="'cax cax-download'"
                                        [model]="getDownloadMenuItems(rowData)"
                                        [disabled]="!canDownload || fileGenerationStatus[rowData.batch_id] === 'generating'">
                                    </cax-splitButton>
                                </div>
                                <!-- case for comments -->
                                <div *ngSwitchCase="'comments'" class="comments-container">
                                    <div class="comment-indicator" (click)="openCommentsSidebar(rowData)">
                                        <i class="cax cax-message-circle comment-icon"></i>
                                    </div>
                                </div>
                                <!-- case for references -->
                                <div
                                        [caxEditableColumn]="col"
                                        [caxEditableColumnField]="col.field"
                                        *ngSwitchCase="'references'"
                                        class="references-container">
                                    <cax-cellEditor>
                                        <ng-template caxTemplate="input">
                                            <input
                                                caxInputText
                                                type="text"
                                                [(ngModel)]="rowData.references" />
                                        </ng-template>

                                        <ng-template caxTemplate="output">
                                            <div *ngFor="let ref of rowData[col.field]">
                                            <a href="{{ ref }}" target="_blank"
                                                >• {{ ref }}</a>
                                            </div>
                                        </ng-template>
                                    </cax-cellEditor>
                                </div>

                                <!-- case for tags -->
                                <div *ngSwitchCase="'tags'" class="tags-container" (click)="handleOverlay($event, rowData, null)" >
                                    <cax-chip
                                    *ngFor="let tag of rowData.labels"
                                    [label]="tag.name"
                                    [style]="{
                                        backgroundColor: tag.bg_colour_code || '#007bff',
                                        color: tag.text_colour_code || '#ffffff',
                                        height: '40px'
                                    }"
                                    [removable]="true"
                                    [size]="size"
                                    (onRemove)="handleTagRemove($event, rowData, tag)"
                                    >
                                    </cax-chip>
                                    <span *ngIf="!rowData.labels || rowData.labels.length === 0">
                                        No tags
                                    </span>
                                <cax-overlayPanel
                                        #tagsPanel
                                        [showCloseIcon]="false"
                                        [dismissable]="true"
                                    >
                                        <div class="tag-container">
                                            <ng-container *ngIf="overlayTags.length > 0; else noTagsTemplate">
                                                <cax-inputtext
                                                    [placeholder]="'Search'"
                                                    [size]="inputSize"
                                                    [leftIcon]="true"
                                                    [clearIcon]="true"
                                                    [(ngModel)]="tagsName"
                                                    (ngModelChange)="onTagSearch($event)"
                                                    [leftIconClass]="'cax cax-magnifier'"
                                                ></cax-inputtext>

                                                <div class="chip-tag">
                                                    <cax-chip
                                                        *ngFor="let tag of filteredTags"
                                                        [label]="tag.name"
                                                        [removable]="false"
                                                        [size]="size"
                                                        [style]="{
                                                            backgroundColor : tag.bg_colour_code || '#007bff',
                                                            color: tag.text_colour_code || '#ffffff',
                                                        }"
                                                        (click)="updateTag(tag)"
                                                    ></cax-chip>
                                                </div>
                                                <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
                                                <div class="create-tag-button">
                                                    <cax-button
                                                        [leftIcon]="'cax cax-add'"
                                                        [label]="'Create New Tag'"
                                                        [severity]="'primary'"
                                                        [link]="true"
                                                        [size]="buttonSize"
                                                        (click)="openCreateTagSidebar()"
                                                    ></cax-button>
                                                </div>
                                            </ng-container>

                                            <ng-template #noTagsTemplate>
                                                <div class="no-tags-wrapper">
                                                    <cax-inputtext
                                                    [placeholder]="'Search'"
                                                    [size]="inputSize"
                                                    [leftIcon]="true"
                                                    [clearIcon]="true"
                                                    [(ngModel)]="tagsName"
                                                    (ngModelChange)="onTagSearch($event)"
                                                    [leftIconClass]="'cax cax-magnifier'"
                                                  ></cax-inputtext>
                                                    <div class="empty-tag">

                                                    <i class="cax cax-tag-bold custom-icon"></i>

                                                    <div class="no-tags-heading">No Tags Found</div>
                                                    <div class="no-tags-desc">
                                                        Tags help organize and categorize batches. You can add new tags to get started.
                                                    </div>
                                                    </div>
                                                    <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
                                                    <div class="create-tag-button">
                                                        <cax-button
                                                            [leftIcon]="'cax cax-add'"
                                                            [label]="'Create New Tag'"
                                                            [severity]="'primary'"
                                                            [link]="true"
                                                            [size]="buttonSize"
                                                            (click)="openCreateTagSidebar()"
                                                        ></cax-button>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </div>
                                    </cax-overlayPanel>

                                </div>

                                <!-- case for accepted rows -->
                                <div
                                    *ngSwitchCase="'accepted'"
                                    class="accepted-rows-container">
                                    <span class="fw-bold">{{
                                        rowData[col.field]
                                    }}</span
                                    ><span class="fw-light"
                                        >/{{ rowData.total_rows }}</span
                                    >
                                </div>
                                <!-- case for accepted_percent -->
                                <div
                                    *ngSwitchCase="'accepted_percent'"
                                    class="accepted-rows-container">
                                    <span class="fw-bold">{{
                                        rowData[col.field]
                                    }}%</span>
                                </div>
                                <!-- case for insufficient_rows -->
                                <div
                                    *ngSwitchCase="'insufficient_rows'"
                                    class="accepted-rows-container">
                                    <span class="fw-bold">{{
                                        rowData[col.field]
                                    }}</span>
                                </div>
                                <!-- case for progress_percent -->
                                <div *ngSwitchCase="'progress_percent'">
                                    <cax-progressBar
                                        [value]="rowData[col.field] || 0"
                                        [showValue]="true"
                                        [unit]="'%'"
                                        [mode]="'determinate'"
                                        [color]="'var(--blue-500)'">
                                    </cax-progressBar>
                                </div>
                                <!-- case for created_at -->
                                <div
                                    *ngSwitchCase="'created_at'"
                                    class="date-container">
                                    <span>{{
                                        rowData[col.field]
                                            ? (rowData[col.field] | date: 'MMM dd, yyyy')
                                            : ''
                                    }}</span>
                                </div>
                                <!-- case for created_by -->
                                <div
                                    *ngSwitchCase="'created_by'"
                                    class="user-container">
                                    <cax-avatar
                                        [label]="
                                            rowData[col.field]
                                                ? rowData[col.field].substring(0, 2).toUpperCase()
                                                : 'U'
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'">
                                    </cax-avatar>
                                    <span class="user-email">{{ rowData[col.field] }}</span>
                                </div>
                                <!-- case for references -->
                                <div
                                    *ngSwitchCase="'references'"
                                    class="references-container">
                                    <a *ngIf="rowData[col.field]"
                                       href="{{ rowData[col.field] }}"
                                       target="_blank"
                                       class="reference-link">
                                        {{ rowData[col.field] }}
                                    </a>
                                    <span *ngIf="!rowData[col.field]" class="no-reference">
                                        No reference
                                    </span>
                                </div>
                                <!-- case for supplier_connector_batch_id -->
                                <div
                                    *ngSwitchCase="'supplier_connector_batch_id'"
                                    class="sc-batch-id-container">
                                    <span>{{ rowData[col.field] || rowData.batch_id }}</span>
                                </div>
                                <!-- case for total_rows -->
                                <div
                                    *ngSwitchCase="'total_rows'"
                                    class="total-rows-container">
                                    <span class="fw-bold">{{ rowData.total_rows || 0 }}</span>
                                </div>

                                <!-- case for total_rows -->
                                <div
                                    *ngSwitchCase="'total_rows'"
                                    class="total-rows-container">
                                    <span class="fw-bold">{{ rowData.total_rows || 0 }}</span>
                                </div>
                                <!-- case for accepted_rows -->
                                <div
                                    *ngSwitchCase="'accepted_rows'"
                                    class="accepted-rows-container">
                                    <span class="fw-bold">{{ rowData.accepted || 0 }}</span>
                                    <span class="fw-light">/{{ rowData.total_rows || 0 }}</span>
                                </div>
                                <!-- case for rejected_rows -->
                                <div
                                    *ngSwitchCase="'rejected_rows'"
                                    class="rejected-rows-container">
                                    <span class="fw-bold">{{ rowData.rejected || 0 }}</span>
                                    <span class="fw-light">/{{ rowData.total_rows || 0 }}</span>
                                </div>
                                <!-- case for actions -->
                                <div
                                    *ngSwitchCase="'actions'"
                                    class="actions-container">
                                    <!-- Processed status: Approve button and download splitButton -->
                                    <div *ngIf="rowData.status === 'PROCESSED'" class="processed-actions">
                                        <cax-button
                                            [leftIcon]="'cax cax-check-circle'"
                                            [severity]="'success'"
                                            [label]="'Approve'"
                                            [outlined]="true"
                                            (click)="approveBatch(rowData.batch_id)">
                                        </cax-button>
                                        <cax-splitButton
                                            [label]="'Download'"
                                            [model]="getActionDownloadMenuItems(rowData)"
                                            [disabled]="fileGenerationStatus[rowData.batch_id] === 'generating'">
                                        </cax-splitButton>
                                    </div>

                                    <!-- Approved status: Download splitButton only -->
                                    <div *ngIf="rowData.status === 'APPROVED'" class="approved-actions">
                                        <cax-splitButton
                                            [label]="'Download'"
                                            [model]="getActionDownloadMenuItems(rowData)"
                                            [disabled]="fileGenerationStatus[rowData.batch_id] === 'generating'">
                                        </cax-splitButton>
                                    </div>

                                    <!-- Delete button for other statuses -->
                                    <cax-button
                                        *ngIf="rowData.status !== 'PROCESSED' && rowData.status !== 'APPROVED'"
                                        class="delete-button"
                                        [leftIcon]="'cax cax-trash-bin-minimalistic'"
                                        [severity]="'danger'"
                                        (click)="deleteBatch(rowData.batch_id)"
                                        [label]="'Delete'"
                                        [outlined]="true">
                                    </cax-button>
                                </div>
                                <!-- case for uploaded_by -->
                                <div
                                    *ngSwitchCase="'uploaded_by'"
                                    class="user-container">
                                    <cax-avatar
                                        [label]="
                                            rowData[col.field]
                                                .substring(0, 2)
                                                .toUpperCase()
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'">
                                    </cax-avatar>
                                </div>
                                <!-- case for assignee -->
                                <div
                                    *ngSwitchCase="'assignee'"
                                    class="user-container">
                                    <cax-avatar
                                        [label]="
                                            rowData[col.field]
                                                .substring(0, 2)
                                                .toUpperCase()
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'">
                                    </cax-avatar>
                                </div>

                                <!-- case for process -->
                                  <div
                                     [caxEditableColumn]="col"
                                     [caxEditableColumnField]="col.field"
                                     *ngSwitchCase="'process'"
                                     class="w-100 h-100"
                                    class="user-container">
                                    <cax-cellEditor>
                                        <ng-template caxTemplate="input">

                                                <input
                                                caxInputText
                                                type="text"
                                                [(ngModel)]="rowData.process" />
                                        </ng-template>

                                        <ng-template caxTemplate="output">
                                            <div >
                                             {{rowData.process}}
                                            </div>
                                        </ng-template>
                                    </cax-cellEditor>
                                </div>
                                <!-- default case -->
                                <div *ngSwitchDefault>
                                    <span *ngIf="rowData[col.field] !== null && rowData[col.field] !== undefined">
                                        {{ rowData[col.field] }}
                                    </span>
                                    <span *ngIf="rowData[col.field] === null || rowData[col.field] === undefined" class="no-data">
                                        -
                                    </span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </cax-table>
        </div>
        <div class="paginator-container">
            <cax-paginator
                [pageLinkSize]="5"
                [currentPageReportTemplate]="
                    'Showing {currentPage} to {totalPages} of {totalRecords} entries'
                "
                [showCurrentPageReport]="true"
                [showFirstLastIcon]="true"
                [totalRecords]="totalRecords"
                [rows]="pageSize"
                [first]="(currentPage - 1) * pageSize"
                [rightAligned]="true"
                [rowsPerPageOptions]="[10, 50, 100]"
                [showJumpToPageInput]="true"
                [showPageLinks]="true"
                (onPageChange)="onPageChange($event)"></cax-paginator>
        </div>
    </cax-tabPanel>
    <cax-tabPanel
        *ngFor="let tab of dynamicTabs"
        [closable]="tab.closable"
        [header]="tab.header">
        <ng-container *ngComponentOutlet="tab.component; inputs: tab.data" />
    </cax-tabPanel>
</cax-tabView>

<!-- column list sidebar -->

<cax-sidebar
    [headerText]="'Columns (' + batchTableColumns.length + ')'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '390px' }"
    [(visible)]="columnSidebarVisible">
    <app-column-list-sidebar
        [columnListData]="batchTableColumns"
        (emitFilteredColumns)="
            filterColumnsList($event)
        "></app-column-list-sidebar>
</cax-sidebar>

<!-- status overlay panel -->

<cax-overlayPanel
    #batchStatusPanel
    [style]="{ width: 'inherit' }"
    [dismissable]="true">
    <div class="batch-status-panel">
        <cax-chip
            *ngFor="let status of batchStatusPanelList"
            [label]="status.name"
            [size]="size"
            [severity]="status.severity"
            [icon]="status.icon"
            (click)="updateBatchStatus(status)"></cax-chip>
    </div>
</cax-overlayPanel>

<!-- batch log overlay panel -->

<cax-overlayPanel
    [style]="{ width: 'inherit' }"
    #batchLogPanel
    [dismissable]="true">
    <cax-timeline [value]="batchLogList">
        <ng-template caxTemplate="content" let-event>
            {{ event }}
        </ng-template>
    </cax-timeline>
</cax-overlayPanel>

<!-- table configuration overlay panel -->

<cax-overlayPanel
    [style]="{ width: '478px' }"
    #tableSizePanel
    [dismissable]="true">
    <cax-tableconfiguration
        (fontSizeChange)="onFontSizeChange($event)"
        (rowHeightChange)="onRowHeightChange($event)"></cax-tableconfiguration>
</cax-overlayPanel>

<!-- filters list overlay panel -->

<cax-overlayPanel
    [style]="{ width: '332px' }"
    #filtersPanel
    [dismissable]="true">
    <app-filters-list-panel
        [filtersApplied]="appliedTableFilters"></app-filters-list-panel>
</cax-overlayPanel>

<!-- upload new batch sidebar -->

<cax-sidebar
    [headerText]="'Upload New Batch'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '558px' }"
    [(visible)]="uploadSidebarVisible">
    <app-upload-batch-sidebar
        [visible]="uploadSidebarVisible"
        [subscriptionId]="subscriptionId"
        (visibleChange)="uploadSidebarVisible = $event"
        (uploadComplete)="onUploadComplete($event)">
    </app-upload-batch-sidebar>
</cax-sidebar>

<!-- multiple selection toolbar -->

<cax-sidebar
    [style]="{
        left: '50%',
        width: 'max-content',
        transform: 'translate(-50%, 0)',
        height: 'auto',
    }"
    [headerStyle]="{
        background: 'var(--neutral-800)',
        color: 'var(--white-100)',
    }"
    [position]="'bottom'"
    [headerText]="selectedBatches.length + ' Selected'"
    [modal]="false"
    [dismissible]="false"
    [(visible)]="selectionSidebarVisible">
    <div class="selection-sidebar-content">
        <div class="fields-container">
            <!-- Status -->
            <div class="input-container">
                <cax-dropdown
                    [labelText]="'Status'"
                    [appendTo] = "'body'"
                    [options]="statusOptions"
                    [(ngModel)]="selectedBatchStatus"
                    [placeholder]="'Change Status'"
                    [style]="{ width: '180px' }"></cax-dropdown>
            </div>

            <!-- ETA -->
            <div class="input-container">
                <cax-inputtext
                    label="Update ETA"
                    [placeholder]="'Change ETA'"
                    [(ngModel)]="selectedBatchETA"
                    [style]="{ width: '180px' }"></cax-inputtext>
            </div>

            <!-- Assignee -->
            <div class="input-container">
                <cax-dropdown
                    [labelText]="'Assignee'"
                    [appendTo] = "'body'"
                    [options]="assigneeOptions"
                    [(ngModel)]="selectedBatchAssignee"
                    [placeholder]="'Change Assignee'"
                    [style]="{ width: '180px' }"></cax-dropdown>
            </div>

            <!-- Comments -->
            <div class="input-container">
                <cax-inputtext
                    [label]="'Add Comments'"
                    [placeholder]="'Enter your comments here...'"
                    [(ngModel)]="selectedBatchesComment"
                    [style]="{ width: '300px' }"></cax-inputtext>
            </div>

            <!-- Buttons -->
            <div class="button-wrapper">
                <cax-button
                    label="Save"
                    [disabled]="!isSaveEnabled() || bulkActionInProgress"
                    (click)="saveAllChanges()"
                    [size]="'medium'">
                </cax-button>

                <div class="divider"></div>

                <!-- Bulk Actions -->
                <cax-splitButton
                    icon="cax cax-download"
                    label=""
                    [disabled]="bulkActionInProgress"
                    [title]="'Bulk actions'">
                    <ng-template caxTemplate="menu">
                        <cax-button
                            *ngIf="canDownload"
                            icon="cax cax-download"
                            label="Download All"
                            (click)="performBulkAction('download')"
                            [disabled]="bulkActionInProgress">
                        </cax-button>
                        <cax-button
                            *ngIf="canApprove"
                            icon="cax cax-check-circle"
                            label="Approve All"
                            (click)="performBulkAction('approve')"
                            [disabled]="bulkActionInProgress">
                        </cax-button>
                        <cax-button
                            *ngIf="canEdit"
                            icon="cax cax-close-circle"
                            label="Cancel All"
                            (click)="performBulkAction('cancel')"
                            [disabled]="bulkActionInProgress">
                        </cax-button>
                        <cax-button
                            *ngIf="canDelete"
                            icon="cax cax-trash"
                            label="Delete All"
                            (click)="performBulkAction('delete')"
                            [disabled]="bulkActionInProgress">
                        </cax-button>
                    </ng-template>
                </cax-splitButton>

                <!-- Retry Button -->
                <cax-button
                    *ngIf="showError"
                    icon="cax cax-refresh"
                    label="Retry"
                    (click)="retryFailedOperation('loadBatches')"
                    [size]="'medium'"
                    severity="secondary">
                </cax-button>
            </div>
        </div>
    </div>
</cax-sidebar>

<!-- reason for rework dialog  -->

<cax-dialog
    [(visible)]="reworkDialogVisible"
    [closable]="false"
    [modal]="true"
    [style]="{ width: '528px' }">
    <app-rework-dialog
        (reworkDialogStatus)="reworkDialogVisible = $event"></app-rework-dialog>
</cax-dialog>

<!-- common confirm dialog -->

<cax-confirmDialog></cax-confirmDialog>

<!-- Toast notifications -->
<cax-toast></cax-toast>

<!-- comments sidebar -->
<cax-sidebar
     [headerStyle]="{
        fontSize: '16px',
    }"
    [headerText]="currentBatchHeader"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '480px' }"
    [(visible)]="commentsSidebarVisible"
    (onHide)="onCommentSidebarClose()"
>
    <cax-comments
        [comments]="currentBatchComments"
        [sidebarHeader]="currentBatchHeader"
        [mentionSuggestions]="mentionSuggestions"
        [hashtagSuggestions]="hashtagSuggestions"
        [isAdmin]="true"
        (commentAdded)="onCommentAdded($event)"
        [(visible)]="commentsSidebarVisible">
    </cax-comments>
</cax-sidebar>



<!-- Tag Sidebar -->
<app-create-new-tag-sidebar
    [(visible)]="isCreateTagSidebarOpen"
    (createTag)="onCreateNewTag($event)"
    (close)="isCreateTagSidebarOpen = false">
</app-create-new-tag-sidebar>

<!-- ETA Calendar Overlay Panel -->
<cax-overlayPanel
    #calendalPanel
    [showCloseIcon]="false"
    [dismissable]="true"
    styleClass="no-padding-overlay"
    [style]="{ width: '300px' }">
    <cax-calendar
        [(ngModel)]="selectedDate"
        [inline]="true"
        inputId="date2"
        (onInlineSaved)="handleDateSaved()"
        (onInlineCancelled)="handleDateCancel()">
    </cax-calendar>
</cax-overlayPanel>
